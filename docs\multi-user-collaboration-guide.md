# Multi-User Collaboration Guide

## 🔐 Row Level Security (RLS) Policies

### Task Update Permissions

The RLS policy for task updates requires that the user must be **one of the following**:

1. **Creator** (`created_by = auth.uid()`) - User who created the task
2. **Assigned User** (`assigned_user_id = auth.uid()`) - Primary assignee
3. **Owner** (`owner_id = auth.uid()`) - Task owner
4. **In Assigned Users Array** (`auth.uid()::text = ANY(assigned_users)`) - Multiple assignees
5. **Admin** (`role = 'admin'`) - Admin users can update any task

### Other Entity Permissions

#### Projects
- **View**: All users can view all projects
- **Create**: Authenticated users can create projects
- **Update/Delete**: Creators and admins only

#### Columns
- **View**: All users can view all kanban columns
- **Create**: Authenticated users can create columns
- **Update/Delete**: Creators and admins only

#### Folders
- **View**: All users can view all folders
- **Create**: Authenticated users can create folders
- **Update/Delete**: Creators and admins only

## 🔄 Version Conflict Resolution

### Optimistic Locking System

The application uses optimistic locking to handle concurrent updates:

1. **Version Field**: Each task has a `version` field that increments on every update
2. **Conflict Detection**: Updates include `WHERE version = expected_version`
3. **Automatic Retry**: Up to 3 retry attempts with fresh version fetching
4. **Graceful Degradation**: Returns latest data if max retries reached

### Retry Logic Flow

```
Attempt 1: Fetch version N → Update to N+1
↓ (Version conflict)
Attempt 2: Fetch version N+1 → Update to N+2
↓ (Success or conflict)
Attempt 3: Fetch version N+2 → Update to N+3
↓ (Max retries)
Return latest data without update
```

### Real-time Sync Considerations

- **Processing Delay**: 300ms delay in real-time handlers to prevent race conditions
- **Version Consistency**: All users receive version updates via real-time sync
- **Conflict Prevention**: Progressive retry delays (300ms, 600ms, 900ms)

## 👥 User Role Management

### Admin Users
- Can update/delete any task, project, column, or folder
- Bypass most RLS restrictions
- Should be used for system administrators
- **Archive Management**: Exclusive access to view and restore archived items
- **System Cleanup**: Can manage archived items and permanent deletion

### Regular Users
- Can only update entities they created or are assigned to
- Must be explicitly assigned to tasks to modify them
- Can view all entities but have limited modification rights
- **Archive Operations**: Can archive their own projects and folders
- **No Archive Access**: Cannot view or restore archived items

### Assigning Users to Tasks

To allow a user to modify a task, use one of these methods:

1. **Primary Assignment**: Set `assigned_user_id`
2. **Multiple Assignment**: Add to `assigned_users` array
3. **Ownership**: Set as `owner_id`
4. **Admin Role**: Grant admin privileges

## 🚨 Common Issues and Solutions

### Issue: User Cannot Update Tasks
**Symptoms**: 406 errors, "Version conflict" messages
**Cause**: User lacks permission due to RLS policy
**Solution**: 
- Assign user to the task
- Grant admin role
- Check task ownership

### Issue: Version Conflicts in Multi-User Environment
**Symptoms**: Repeated retry attempts with same version
**Cause**: Race conditions between users
**Solution**: 
- Automatic retry logic handles this
- Real-time delays prevent immediate conflicts
- Users see eventual consistency

### Issue: Real-time Updates Not Working
**Symptoms**: Changes not appearing for other users
**Cause**: Subscription failures or network issues
**Solution**:
- Check real-time connection status
- Enable polling fallback
- Force refresh data

## 🛠️ Debug Commands

### Check User Permissions
```javascript
// Check current user's role
window.supabase.auth.getUser().then(console.log);

// Check task ownership
window.supabase.from('tasks').select('id, title, created_by, assigned_user_id, owner_id, assigned_users').eq('id', 'task-id').single().then(console.log);
```

### Check Version Status
```javascript
// Check task version
window.useSupabaseStore.getState().checkTaskVersion('task-id');

// Check real-time connection
window.useSupabaseStore.getState().testRealtimeConnection();
```

### Force Refresh
```javascript
// Refresh all data
window.useSupabaseStore.getState().forceRefresh();

// Refresh specific task
window.useSupabaseStore.getState().refreshTask('task-id');
```

## 📋 Best Practices

### For Administrators

1. **Grant Appropriate Roles**: Only give admin access to trusted users
2. **Assign Tasks Properly**: Ensure users are assigned to tasks they need to modify
3. **Monitor Permissions**: Regularly review user roles and assignments
4. **Test Multi-User Scenarios**: Verify collaboration works before deployment

### For Developers

1. **Handle Version Conflicts**: Always implement retry logic for updates
2. **Check Permissions**: Validate user permissions before attempting updates
3. **Use Real-time Wisely**: Add delays to prevent race conditions
4. **Provide Feedback**: Show users when updates fail due to permissions
5. **Test Edge Cases**: Verify behavior with concurrent users

### For Users

1. **Refresh When Needed**: Use force refresh if data seems out of sync
2. **Check Assignments**: Ensure you're assigned to tasks you need to modify
3. **Report Issues**: Contact admin if you can't update tasks you should be able to
4. **Be Patient**: Allow time for real-time updates to propagate

## 🔧 Configuration

### Database Setup
- RLS policies are defined in `supabase-schema.sql`
- Version field is automatically managed
- Real-time subscriptions are configured in `useSupabaseStore.ts`

### Environment Variables
- Supabase URL and keys in `.env`
- Real-time configuration in Supabase dashboard
- User roles managed in `user_profiles` table

## 📊 Monitoring

### Key Metrics to Watch
- Version conflict frequency
- Real-time subscription success rate
- User permission errors
- Update retry patterns

### Logging
- Version conflict details in browser console
- Real-time connection status
- Permission errors with user context
- Retry attempt progression
