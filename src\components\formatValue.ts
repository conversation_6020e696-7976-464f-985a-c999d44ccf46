// formatValue.ts

interface ColumnType {
    id: string;
    title: string;
  }

  interface UserType {
    id: string;
    name: string;
  }

  interface FolderType {
    id: string;
    name: string;
    parentId?: string;
  }

export function formatValue(field: string, value: string, columns: ColumnType[], users: UserType[], folders?: FolderType[]) {
    try {
        let parsed: string;
        try {
            parsed = JSON.parse(value);
         } catch {
        parsed = value;
      }
  
      // Handle status field to show column labels
      if (field === 'status') {
        const column = columns.find(col => col.id === parsed);
        return column ? column.title : parsed;
      }
  
      // Handle user ID fields
      if (field === 'assignedUserId' || field === 'userId') {
        const user = users.find(u => u.id === parsed);
        return user ? user.name : parsed;
      }
  
      // Handle assignedUsers field
      if (field === 'assignedUsers' && Array.isArray(parsed)) {
        const userNames = parsed.map((id: string) => {
          const user = users.find(u => u.id === id);
          return user ? user.name : id;
        });
        return JSON.stringify(userNames);
      }

      // Handle folderId field
      if (field === 'folderId' && folders) {
        if (parsed === null || parsed === '' || parsed === undefined) {
          return 'No folder';
        }
        const folder = folders.find(f => f.id === parsed);
        return folder ? folder.name : parsed;
      }

      // Handle projectId field (could be enhanced similarly if needed)
      if (field === 'projectId') {
        if (parsed === null || parsed === '' || parsed === undefined) {
          return 'No project';
        }
        return parsed; // Could be enhanced to show project name if projects array is passed
      }

      if (typeof parsed === 'object') {
        return JSON.stringify(parsed);
      }
      return parsed;
    } catch {
      return value;
    }
  }
  