# TaskFlow - Multi-User Project Management Tool

A comprehensive project management application with multi-user authentication, real-time collaboration, and cloud-based data persistence powered by Supabase.

## 🚀 Features

### Core Project Management
- **Task Management**: Create, assign, and track tasks with priorities, due dates, and status updates
- **Custom Fields**: Extend tasks with custom text, number, date, and dropdown fields
- **Project Organization**: Organize tasks into projects and folders
- **Kanban Board**: Visual task management with drag-and-drop functionality
- **Timeline View**: Gantt-style timeline for project planning
- **Resource Management**: User capacity planning and skillset tracking

### Multi-User Collaboration
- **User Authentication**: Secure email/password authentication
- **Role-Based Access**: Admin and user roles with appropriate permissions
- **Real-Time Updates**: Live collaboration with instant data synchronization
- **User Management**: Admin tools for managing users and permissions
- **Team Organization**: User groups and skillset assignments

### Data Management
- **Cloud Storage**: All data stored securely in Supabase
- **Import/Export**: JSON-based backup and restore functionality
- **Data Synchronization**: Automatic sync with manual refresh option
- **Offline Resilience**: Optimistic updates with conflict resolution
- **Archive System**: Soft-delete functionality with admin restore capabilities
- **Data Retention**: 1-week retention for archived items with automatic cleanup

## 🛠 Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand with Supabase integration
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Icons**: Lucide React
- **Date Handling**: date-fns

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account
- Modern web browser

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd project-management-tool
npm install
```

### 2. Set Up Supabase
Follow the detailed [Supabase Setup Guide](./SUPABASE_SETUP.md) to:
- Create a Supabase project
- Configure environment variables
- Set up database schema
- Create the super admin user

### 3. Start Development Server
```bash
npm run dev
```

### 4. Access the Application
- Open http://localhost:5173
- Log in with super admin credentials:
  - Email: `<EMAIL>`
  - Password: `Vklonis123@$`

## 👤 User Roles

### Administrator
- Full system access
- User management capabilities
- System-wide import/export
- All project and task permissions
- Archive management and restore capabilities
- Access to archived items and cleanup operations

### User
- Create and manage own tasks/projects
- Collaborate on assigned tasks
- Limited import/export (own data)
- View team resources and capacity

## 🔐 Security Features

- **Row Level Security (RLS)**: Database-level access control
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Permissions**: Granular access control
- **Data Isolation**: Users only see authorized data
- **Secure API**: All requests authenticated and authorized

## 📊 Key Components

### Authentication System
- Login/Register forms with validation
- Password reset functionality
- Session management and persistence
- Protected routes and components

### Data Layer
- Supabase service layer for all CRUD operations
- Real-time subscriptions for live updates
- Optimistic updates with rollback
- Conflict resolution and sync management

### User Interface
- Responsive design for all screen sizes
- Loading states and error handling
- Real-time collaboration indicators
- Intuitive navigation and workflows

## 🔄 Data Migration

### From Local Version
If upgrading from the local-only version:

1. **Export existing data** using the old version's export feature
2. **Set up Supabase** following the setup guide
3. **Import data** using the new version's import feature
4. **Verify migration** by checking all data is present

### Backup Strategy
- **Regular exports**: Use the built-in export feature
- **Database backups**: Configure Supabase backup policies
- **Version control**: Keep schema changes in version control

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run preview
```

### Environment Variables
Required for all environments:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

- **Setup Issues**: Check the [Supabase Setup Guide](./SUPABASE_SETUP.md)
- **Authentication Problems**: Verify environment variables and Supabase configuration
- **Data Issues**: Check browser console and Supabase logs
- **Performance**: Monitor real-time subscription usage

## 🔮 Roadmap

- [ ] Mobile app development
- [ ] Advanced reporting and analytics
- [ ] Integration with external tools
- [ ] Advanced workflow automation
- [ ] Enhanced notification system
- [ ] File attachment support

## 📈 Performance Considerations

- **Real-time subscriptions**: Automatically managed for optimal performance
- **Data caching**: Local state management with server sync
- **Optimistic updates**: Immediate UI feedback with server confirmation
- **Lazy loading**: Components and data loaded as needed

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check environment variables
   - Verify Supabase project configuration
   - Ensure user exists and has correct role

2. **Data Sync Issues**
   - Check network connectivity
   - Verify Supabase service status
   - Use manual sync button if needed

3. **Permission Errors**
   - Verify user role assignments
   - Check RLS policies in Supabase
   - Ensure proper authentication state

### Debug Mode
Enable debug logging by adding to your environment:
```
VITE_DEBUG=true
```

This will provide detailed logging for troubleshooting.

## 🔧 Supabase Integration Details

### Database Architecture
- **PostgreSQL** with advanced features (JSONB, arrays, triggers)
- **Row Level Security (RLS)** for data protection
- **Automatic user profile creation** via database triggers
- **JSONB subtasks** for nested task structure
- **Comprehensive audit trails** for all changes

### Key Tables
- `user_profiles` - Extended user information with roles and groups
- `tasks` - Main task management with JSONB subtasks support
- `projects` - Project organization and planning
- `user_groups` - Team organization and collaboration
- `skillset_groups` - Skills and competency management
- `user_capacities` - Resource planning and availability
- `task_comments` - Threaded comments on tasks and subtasks
- `task_history` - Complete change tracking and audit trail
- Archive columns on all main tables (`archived_at`, `archived_by`, `original_*_id`)
- Archive management functions (`archive_folder_cascade`, `archive_project_cascade`, etc.)

### Security Features
```sql
-- Users can update own profile
CREATE POLICY "Users can update own profile" ON user_profiles
FOR UPDATE USING (auth.uid() = id);

-- Admins can update any profile
CREATE POLICY "Admins can update any profile" ON user_profiles
FOR UPDATE USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### Advanced Features
- **Subtask Management**: Full CRUD operations with comments and history
- **Resource Management**: Capacity planning and skillset matching
- **Real-time Capabilities**: Currently disabled for stability
- **Optimistic Updates**: Immediate UI feedback with error recovery

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
- Check the [Supabase Setup Guide](./SUPABASE_SETUP.md)
- Review the [Troubleshooting Guide](./docs/troubleshooting.md)
- Check [Database Schema Documentation](./docs/database-schema.md)
- Create an issue with detailed information

## 🔗 Related Documentation

- [Supabase Setup Guide](./SUPABASE_SETUP.md) - Detailed database setup
- [Database Schema](./docs/database-schema.md) - Complete schema documentation
- [Custom Fields Guide](./docs/custom-fields-guide.md) - Custom fields functionality
- [Troubleshooting Guide](./docs/troubleshooting.md) - Common issues and solutions
- [Resource Capacity Management Guide](./docs/resource-capacity-management-guide.md) - Capacity planning features
- [Supabase Documentation](https://supabase.com/docs) - Official Supabase docs
