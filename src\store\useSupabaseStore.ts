import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { supabase, getCurrentUser } from '../lib/supabase';
import {
  Task,
  Project,
  UserGroup,
  KanbanColumn,
  Folder,
  TaskComment,
  TaskHistoryEntry,
  User,
  SkillsetGroup,
  UserCapacity,
  TaskEffort,
  TaskFilter,
  Subtask,
  TaskDependency
} from '../types';
import {
  taskService,
  projectService,
  userGroupService,
  columnService,
  folderService,
  userProfileService,
  skillsetService,
  capacityService,
  effortService,
  commentService,
  historyService,
  durationService,
  notificationService,
  automationWorkflowService,
  automationExecutionService,
  automationTriggerService,
  dependencyService,
  dateCalculationService,
  customFieldService
} from '../services/supabaseService';
import { dependencyPropagationService } from '../services/dependencyPropagationService';
import { automationEngine } from '../services/automationEngine';
import { cloneService, CloneOptions } from '../services/cloneService';

interface SupabaseStoreState {
  // Data
  tasks: Task[];
  projects: Project[];
  userGroups: UserGroup[];
  columns: KanbanColumn[];
  folders: Folder[];
  users: User[];
  skillsetGroups: SkillsetGroup[];
  userCapacities: UserCapacity[];
  taskEfforts: TaskEffort[];
  notifications: Notification[];
  taskDependencies: TaskDependency[];
  customFields: any[];
  
  // UI State
  tasksViewMode: 'kanban' | 'list' | 'timeline';
  selectedTreeNode: string | null;
  expandedTreeNodes: Set<string>;
  taskFilters: TaskFilter;
  
  // Loading states
  loading: {
    tasks: boolean;
    projects: boolean;
    userGroups: boolean;
    columns: boolean;
    folders: boolean;
    users: boolean;
    skillsetGroups: boolean;
    userCapacities: boolean;
    taskEfforts: boolean;
    notifications: boolean;
    taskDependencies: boolean;
    customFields: boolean;
  };

  // Initialization
  initialized: boolean;

  // Refresh control
  lastRefresh: number;

  // Multi-user conflict prevention
  isUpdating: boolean;
  isSyncing: boolean;
  syncFailureCount: number;
  updateCounter: number;
  sessionTimeout: NodeJS.Timeout | null;
  syncInterval: NodeJS.Timeout | null;

  // Real-time connection management
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  reconnectAttempts: number;
  lastConnectionTime: number;
  maxReconnectAttempts: number;

  // Real-time collaboration
  activeUsers: { [entityId: string]: Array<{ user_id: string; email: string; online_at: string }> };
  presenceChannels: { [entityId: string]: any };
  enablePolling: boolean;
  
  // Actions
  initialize: () => Promise<void>;
  
  // Task actions
  addTask: (task: Omit<Task, 'id' | 'comments' | 'history' | 'durations' | 'subtasks'>) => Promise<void>;
  updateTask: (id: string, task: Partial<Task>) => Promise<void>;
  moveTask: (taskId: string, newStatus: Task['status']) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  cloneTask: (taskId: string, options?: CloneOptions) => Promise<void>;
  
  // Comment actions
  addComment: (taskId: string, comment: Omit<TaskComment, 'id' | 'timestamp' | 'taskId'>) => Promise<TaskComment>;
  updateComment: (taskId: string, commentId: string, content: string) => Promise<void>;
  deleteComment: (taskId: string, commentId: string) => Promise<void>;

  // Subtask comment actions
  addSubtaskComment: (taskId: string, subtaskId: string, comment: Omit<TaskComment, 'id' | 'timestamp' | 'taskId'>) => Promise<TaskComment>;
  updateSubtaskComment: (taskId: string, subtaskId: string, commentId: string, content: string) => Promise<void>;
  deleteSubtaskComment: (taskId: string, subtaskId: string, commentId: string) => Promise<void>;

  // Subtask actions
  addSubtask: (taskId: string, subtask: Omit<Subtask, 'id'>) => Promise<void>;
  updateSubtask: (taskId: string, subtaskId: string, updates: Partial<Subtask>) => Promise<void>;
  deleteSubtask: (taskId: string, subtaskId: string) => Promise<void>;

  // Project actions
  addProject: (project: Omit<Project, 'id' | 'tasks'>) => Promise<void>;
  updateProject: (id: string, project: Partial<Project>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  cloneProject: (projectId: string, options?: CloneOptions) => Promise<void>;
  
  // User Group actions
  addUserGroup: (group: Omit<UserGroup, 'id'>) => Promise<void>;
  updateUserGroup: (id: string, group: Partial<UserGroup>) => Promise<void>;
  deleteUserGroup: (id: string) => Promise<void>;
  
  // Column actions
  addColumn: (column: Omit<KanbanColumn, 'id'>) => Promise<void>;
  updateColumn: (id: string, column: Partial<KanbanColumn>) => Promise<void>;
  deleteColumn: (id: string) => Promise<void>;
  reorderColumns: (columns: KanbanColumn[]) => Promise<void>;
  
  // Folder actions
  addFolder: (folder: Omit<Folder, 'id'>) => Promise<void>;
  updateFolder: (id: string, folder: Partial<Folder>) => Promise<void>;
  deleteFolder: (id: string) => Promise<void>;
  
  // User actions
  addUser: (user: Omit<User, 'id'>) => Promise<void>;
  updateUser: (id: string, user: Partial<User>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  
  // Skillset actions
  addSkillsetGroup: (skillset: Omit<SkillsetGroup, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateSkillsetGroup: (id: string, skillset: Partial<SkillsetGroup>) => Promise<void>;
  deleteSkillsetGroup: (id: string) => Promise<void>;
  
  // Capacity actions
  addUserCapacity: (capacity: Omit<UserCapacity, 'id'>) => Promise<void>;
  updateUserCapacity: (id: string, capacity: Partial<UserCapacity>) => Promise<void>;
  deleteUserCapacity: (id: string) => Promise<void>;
  
  // Effort actions
  addTaskEffort: (effort: Omit<TaskEffort, 'id'>) => Promise<void>;
  updateTaskEffort: (id: string, effort: Partial<TaskEffort>) => Promise<void>;
  deleteTaskEffort: (id: string) => Promise<void>;

  // Notification actions
  loadNotifications: (userId: string) => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  markNotificationAsUnread: (notificationId: string) => Promise<void>;
  markAllNotificationsAsRead: (userId: string) => Promise<void>;
  getUnreadNotificationCount: (userId: string) => number;

  // UI actions
  setTasksViewMode: (mode: 'kanban' | 'list' | 'timeline') => void;
  setSelectedTreeNode: (nodeId: string | null) => void;
  toggleTreeNode: (nodeId: string) => void;
  setTaskFilters: (filters: Partial<TaskFilter>) => void;
  clearTaskFilters: () => void;
  
  // Sync actions
  syncData: () => Promise<void>;
  syncTasks: () => Promise<void>;
  syncProjects: () => Promise<void>;
  syncUserGroups: () => Promise<void>;
  setupRealtimeSubscriptions: () => void;
  attemptRealtimeConnection: () => void;
  onConnectionSuccess: () => void;
  onConnectionError: () => void;
  cleanupSubscriptions: () => void;
  cleanup: () => void;

  // Real-time methods
  handleTaskChange: (payload: any) => void;
  handleProjectChange: (payload: any) => void;
  handleColumnChange: (payload: any) => void;
  handleFolderChange: (payload: any) => void;
  handleUserProfileChange: (payload: any) => void;
  handleSkillsetChange: (payload: any) => void;
  handleCapacityChange: (payload: any) => void;
  handleNotificationChange: (payload: any) => void;
  handleCommentChange: (payload: any) => void;
  handleDependencyChange: (payload: any) => void;
  trackUserPresence: (entityType: string, entityId: string) => void;
  stopTrackingPresence: (entityType: string, entityId: string) => void;
  startPolling: () => void;
  stopPolling: () => void;

  // Debug and testing methods
  testRealtimeConnection: () => void;
  forceRefresh: () => Promise<void>;
  refreshTask: (taskId: string) => Promise<Task | undefined>;
  checkTaskVersion: (taskId: string) => Promise<void>;

  // Queue management for multi-user conflict prevention
  queueUpdate: (updateFn: () => Promise<void>) => Promise<void>;

  // Session health check
  checkSessionHealth: () => Promise<boolean>;
  resetOnStaleSession: () => Promise<void>;

  // Manual refresh
  manualRefresh: () => Promise<void>;

  // Dependency management
  loadTaskDependencies: (taskId: string) => Promise<void>;
  createTaskDependency: (dependency: any) => Promise<any>;
  updateTaskDependency: (id: string, updates: any) => Promise<any>;
  deleteTaskDependency: (id: string) => Promise<any>;

  // Custom Field actions
  loadCustomFields: () => Promise<void>;
  addCustomField: (field: any) => Promise<void>;
  updateCustomField: (id: string, updates: any) => Promise<void>;
  deleteCustomField: (id: string) => Promise<void>;
  reorderCustomFields: (fieldIds: string[]) => Promise<void>;
}

// Helper function to transform Supabase data to app format
const transformSupabaseTask = (supabaseTask: any): Task => {
  return {
    id: supabaseTask.id,
    title: supabaseTask.title,
    description: supabaseTask.description,
    status: supabaseTask.status,
    priority: supabaseTask.priority,
    assignedUserId: supabaseTask.assigned_user_id,
    assignedUsers: supabaseTask.assigned_users || [],
    assignedGroups: supabaseTask.assigned_groups || [],
    ownerId: supabaseTask.owner_id,
    dueDate: supabaseTask.due_date,
    startDate: supabaseTask.start_date,
    tags: supabaseTask.tags || [],
    projectId: supabaseTask.project_id,
    folderId: supabaseTask.folder_id,
    comments: supabaseTask.task_comments?.map((comment: any) => ({
      id: comment.id,
      taskId: comment.task_id,
      userId: comment.user_id,
      content: comment.content,
      timestamp: comment.created_at,
      edited: comment.edited,
      parentId: comment.parent_id,
      replies: []
    })) || [],
    history: supabaseTask.task_history?.map((entry: any) => ({
      id: entry.id,
      taskId: entry.task_id,
      timestamp: entry.created_at,
      field: entry.field,
      oldValue: entry.old_value,
      newValue: entry.new_value,
      userId: entry.user_id
    })) || [],
    durations: supabaseTask.task_durations?.map((duration: any) => ({
      status: duration.status,
      startTime: duration.start_time,
      endTime: duration.end_time
    })) || [],
    subtasks: Array.isArray(supabaseTask.subtasks) ? supabaseTask.subtasks : [],
    effort: supabaseTask.effort,
    customFieldValues: supabaseTask.custom_field_values || {}
  };
};

const transformSupabaseProject = (supabaseProject: any): Project => {
  return {
    id: supabaseProject.id,
    name: supabaseProject.name,
    description: supabaseProject.description,
    color: supabaseProject.color,
    startDate: supabaseProject.start_date,
    endDate: supabaseProject.end_date,
    folderId: supabaseProject.folder_id,
    effort: supabaseProject.effort,
    tasks: [] // Will be populated from tasks
  };
};

const transformSupabaseUserGroup = (supabaseGroup: any): UserGroup => {
  return {
    id: supabaseGroup.id,
    name: supabaseGroup.name,
    color: supabaseGroup.color
  };
};

const transformSupabaseColumn = (supabaseColumn: any): KanbanColumn => {
  // Map column titles to status values for compatibility
  const statusMap: { [key: string]: string } = {
    'To Do': 'todo',
    'In Progress': 'in-progress',
    'Review': 'review',
    'Done': 'done'
  };

  return {
    id: statusMap[supabaseColumn.title] || supabaseColumn.title.toLowerCase().replace(/\s+/g, '-'),
    title: supabaseColumn.title,
    color: supabaseColumn.color,
    dbId: supabaseColumn.id // Keep the database ID for updates
  };
};

const transformSupabaseFolder = (supabaseFolder: any): Folder => {
  return {
    id: supabaseFolder.id,
    name: supabaseFolder.name,
    parentId: supabaseFolder.parent_id
  };
};

const transformSupabaseUser = (supabaseUser: any): User => {
  return {
    id: supabaseUser.id,
    name: supabaseUser.name,
    email: supabaseUser.email,
    groupId: supabaseUser.group_id || undefined,
    avatar: supabaseUser.avatar_url || undefined,
    skillsetIds: supabaseUser.skillset_ids || []
  };
};

const transformSupabaseSkillset = (supabaseSkillset: any): SkillsetGroup => {
  return {
    id: supabaseSkillset.id,
    name: supabaseSkillset.name,
    description: supabaseSkillset.description,
    color: supabaseSkillset.color,
    createdAt: supabaseSkillset.created_at,
    updatedAt: supabaseSkillset.updated_at
  };
};

const transformSupabaseCapacity = (supabaseCapacity: any): UserCapacity => {
  return {
    id: supabaseCapacity.id, // Include database ID for updates
    userId: supabaseCapacity.user_id,
    dailyHours: supabaseCapacity.daily_hours,
    weeklyHours: supabaseCapacity.weekly_hours,
    workingDays: supabaseCapacity.working_days,
    effectiveFrom: supabaseCapacity.effective_from,
    effectiveTo: supabaseCapacity.effective_to
  };
};

const transformSupabaseEffort = (supabaseEffort: any): TaskEffort => {
  return {
    taskId: supabaseEffort.task_id,
    estimatedHours: supabaseEffort.estimated_hours,
    actualHours: supabaseEffort.actual_hours,
    assignedUserId: supabaseEffort.assigned_user_id,
    requiredSkillsets: supabaseEffort.required_skillsets || []
  };
};

export const useSupabaseStore = create<SupabaseStoreState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    tasks: [],
    projects: [],
    userGroups: [],
    columns: [],
    folders: [],
    users: [],
    skillsetGroups: [],
    userCapacities: [],
    taskEfforts: [],
    notifications: [],
    taskDependencies: [],
    customFields: [],

    tasksViewMode: 'kanban',
    selectedTreeNode: null,
    expandedTreeNodes: new Set(),
    taskFilters: {
      assignedUsers: [],
      status: [],
      priority: [],
      owners: [],
      assignedGroups: [],
      tags: [],
      dueDateRange: {},
      hasOverdueTasks: false,
      search: undefined,
    },

    loading: {
      tasks: false,
      projects: false,
      userGroups: false,
      columns: false,
      folders: false,
      users: false,
      skillsetGroups: false,
      userCapacities: false,
      taskEfforts: false,
      notifications: false,
      taskDependencies: false,
      customFields: false,
    },

    initialized: false,
    lastRefresh: 0,
    isUpdating: false,
    isSyncing: false,
    syncFailureCount: 0,
    updateCounter: 0,
    sessionTimeout: null,
    syncInterval: null,

    // Real-time connection management initial state
    connectionState: 'disconnected',
    reconnectAttempts: 0,
    lastConnectionTime: 0,
    maxReconnectAttempts: 5,

    // Real-time collaboration initial state
    activeUsers: {},
    presenceChannels: {},
    enablePolling: false,

    // Initialize store with data from Supabase
    initialize: async () => {
      const state = get();
      if (state.initialized) {
        console.log('Store already initialized, skipping...');
        return;
      }

      // Set initialized immediately to prevent double initialization
      set({ initialized: true });
      console.log('Starting store initialization...');

      try {
        // Clear any existing state first
        set({
          tasks: [],
          projects: [],
          userGroups: [],
          columns: [],
          folders: [],
          users: [],
          skillsetGroups: [],
          userCapacities: [],
          taskEfforts: [],
          isUpdating: false,
          isSyncing: false,
          syncFailureCount: 0,
          updateCounter: 0,
          activeUsers: {},
          presenceChannels: {},
          enablePolling: false,
          initialized: true
        });

        await state.syncData();
        state.setupRealtimeSubscriptions();

        // Initialize automation engine
        try {
          await automationEngine.initialize();
          console.log('✅ Automation engine initialized');
        } catch (error) {
          console.warn('Failed to initialize automation engine:', error);
        }

        // Clear any existing intervals first
        const currentState = get();
        if (currentState.syncInterval) {
          clearInterval(currentState.syncInterval);
        }
        if (currentState.sessionTimeout) {
          clearInterval(currentState.sessionTimeout);
        }

        // Temporarily disable periodic sync since it's causing timeouts
        // const syncInterval = setInterval(async () => {
        //   try {
        //     console.log('Performing periodic sync for multi-user updates...');
        //     await get().syncData();
        //   } catch (error) {
        //     console.error('Periodic sync failed:', error);
        //   }
        // }, 60000); // Sync every 60 seconds (reduced frequency)
        const syncInterval = null;

        // Disable health check temporarily to avoid interference
        // const healthCheckInterval = setInterval(async () => {
        //   const isHealthy = await state.checkSessionHealth();
        //   if (!isHealthy) {
        //     console.warn('Session unhealthy, resetting...');
        //     const currentState = get();
        //     if (currentState.syncInterval) clearInterval(currentState.syncInterval);
        //     if (currentState.sessionTimeout) clearInterval(currentState.sessionTimeout);
        //     await state.resetOnStaleSession();
        //   }
        // }, 300000); // Check every 5 minutes
        const healthCheckInterval = null;

        set({
          sessionTimeout: healthCheckInterval,
          syncInterval: syncInterval
        });

        console.log('Store initialization completed successfully');
      } catch (error) {
        console.error('Failed to initialize store:', error);
      }
    },

    // Debounced refresh to prevent excessive calls
    debouncedRefresh: async () => {
      const now = Date.now();
      const state = get();

      // Only refresh if it's been more than 2 seconds since last refresh
      if (now - state.lastRefresh < 2000) {
        return;
      }

      set({ lastRefresh: now });
      await state.syncData();
    },

    // Sync all data from Supabase with concurrency protection and circuit breaker
    syncData: async () => {
      const state = get();

      // Prevent concurrent sync operations
      if (state.isSyncing) {
        console.log('Sync already in progress, skipping...');
        return;
      }

      // Circuit breaker: if too many failures, skip sync temporarily
      if (state.syncFailureCount >= 3) {
        console.log('Circuit breaker open: too many sync failures, skipping...');
        return;
      }

      console.log('Starting data sync...');
      set({ isSyncing: true });

      set(state => ({
        loading: {
          ...state.loading,
          tasks: true,
          projects: true,
          userGroups: true,
          columns: true,
          folders: true,
          users: true,
          skillsetGroups: true,
          userCapacities: true,
          taskEfforts: true,
        }
      }));

      try {
        // Add timeout protection to prevent hanging operations with individual query logging
        console.log('Starting individual service calls...');

        // Re-enable all functionality for full debugging
        const syncPromise = Promise.all([
          taskService.getTasks().then(data => { console.log('Tasks loaded:', data.length); return data; }),
          projectService.getProjects().then(data => { console.log('Projects loaded:', data.length); return data; }),
          userGroupService.getUserGroups().then(data => { console.log('User groups loaded:', data.length); return data; }),
          columnService.getColumns().then(data => { console.log('Columns loaded:', data.length); return data; }),
          folderService.getFolders().then(data => { console.log('Folders loaded:', data.length); return data; }),
          userProfileService.getAllUsers().then(data => { console.log('Users loaded:', data.length); return data; }),
          skillsetService.getSkillsetGroups().then(data => { console.log('Skillsets loaded:', data.length); return data; }),
          capacityService.getUserCapacities().then(data => { console.log('Capacities loaded:', data.length); return data; }),
          effortService.getTaskEfforts().then(data => { console.log('Efforts loaded:', data.length); return data; }),
          customFieldService.getCustomFields().then(data => { console.log('Custom fields loaded:', data.length); return data; })
        ]);

        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Sync operation timeout')), 10000)
        );

        const result = await Promise.race([syncPromise, timeoutPromise]);

        const [
          supabaseTasks,
          supabaseProjects,
          supabaseUserGroups,
          supabaseColumns,
          supabaseFolders,
          supabaseUsers,
          supabaseSkillsets,
          supabaseCapacities,
          supabaseEfforts,
          supabaseCustomFields
        ] = result as any[];

        console.log('All service calls completed, setting state...');

        // Debug: Check total comments loaded across all tasks
        const totalComments = supabaseTasks.reduce((total, task) => total + (task.task_comments?.length || 0), 0);
        console.log('Debug - Total comments loaded across all tasks:', totalComments);

        // Debug: Check if durations, history, and comments are being loaded for test task
        const testTask = supabaseTasks.find(t => t.title?.includes('2 Test Task 1753386108569'));
        if (testTask) {
          console.log('Debug - Test task raw data:', {
            title: testTask.title,
            task_durations: testTask.task_durations?.length || 0,
            task_history: testTask.task_history?.length || 0,
            task_comments: testTask.task_comments?.length || 0
          });
        }

        const transformedTasks = supabaseTasks.map(transformSupabaseTask);

        // Debug: Check transformed task
        const transformedTestTask = transformedTasks.find(t => t.title?.includes('2 Test Task 1753386108569'));
        if (transformedTestTask) {
          console.log('Debug - Test task transformed:', {
            title: transformedTestTask.title,
            durations: transformedTestTask.durations?.length || 0,
            history: transformedTestTask.history?.length || 0,
            comments: transformedTestTask.comments?.length || 0
          });
        }

        set({
          tasks: transformedTasks,
          projects: supabaseProjects.map(transformSupabaseProject),
          userGroups: supabaseUserGroups.map(transformSupabaseUserGroup),
          columns: supabaseColumns.map(transformSupabaseColumn),
          folders: supabaseFolders.map(transformSupabaseFolder),
          users: supabaseUsers.map(transformSupabaseUser),
          skillsetGroups: supabaseSkillsets.map(transformSupabaseSkillset),
          userCapacities: supabaseCapacities.map(transformSupabaseCapacity),
          taskEfforts: supabaseEfforts.map(transformSupabaseEffort),
          customFields: supabaseCustomFields.map((field: any) => ({
            id: field.id,
            name: field.name,
            label: field.label,
            fieldType: field.field_type,
            isRequired: field.is_required,
            dropdownOptions: field.dropdown_options,
            defaultValue: field.default_value,
            description: field.description,
            sortOrder: field.sort_order,
            isActive: field.is_active,
            createdAt: field.created_at,
            updatedAt: field.updated_at,
            createdBy: field.created_by,
          })),
          loading: {
            tasks: false,
            projects: false,
            userGroups: false,
            columns: false,
            folders: false,
            users: false,
            skillsetGroups: false,
            userCapacities: false,
            taskEfforts: false,
            notifications: false,
          }
        });
        console.log('Data sync completed successfully:', {
          tasks: supabaseTasks.length,
          projects: supabaseProjects.length,
          userGroups: supabaseUserGroups.length,
          columns: supabaseColumns.length,
          folders: supabaseFolders.length,
          users: supabaseUsers.length,
          skillsets: supabaseSkillsets.length,
          capacities: supabaseCapacities.length,
          efforts: supabaseEfforts.length
        });

        // Reset failure count on success
        set({ syncFailureCount: 0 });

      } catch (error) {
        console.error('Failed to sync data:', error);

        // If timeout during periodic sync, don't overwrite existing data
        if ((error as any)?.message === 'Sync operation timeout') {
          console.log('Timeout detected during periodic sync, preserving existing data...');
          // Don't attempt fallback during periodic sync - just log and continue
          // The existing data is still good, we just failed to refresh it
          console.log('Skipping fallback to preserve existing functional state');
        }

        // Increment failure count for circuit breaker
        set(state => ({
          syncFailureCount: state.syncFailureCount + 1,
          loading: {
            tasks: false,
            projects: false,
            userGroups: false,
            columns: false,
            folders: false,
            users: false,
            skillsetGroups: false,
            userCapacities: false,
            taskEfforts: false,
            notifications: false,
          }
        }));

        // If too many failures, clear the sync interval temporarily
        const currentState = get();
        if (currentState.syncFailureCount >= 3 && currentState.syncInterval) {
          console.log('Too many sync failures, disabling periodic sync temporarily');
          clearInterval(currentState.syncInterval);
          set({ syncInterval: null });

          // Re-enable sync after 5 minutes
          setTimeout(() => {
            console.log('Re-enabling periodic sync after cooldown');
            set({ syncFailureCount: 0 });
            // Restart sync interval
            const newSyncInterval = setInterval(async () => {
              try {
                console.log('Performing periodic sync for multi-user updates...');
                await get().syncData();
              } catch (error) {
                console.error('Periodic sync failed:', error);
              }
            }, 60000);
            set({ syncInterval: newSyncInterval });
          }, 300000); // 5 minutes
        }
      } finally {
        // Always clear the sync lock
        set({ isSyncing: false });
      }
    },

    // Individual sync functions for real-time updates
    syncTasks: async () => {
      try {
        const supabaseTasks = await taskService.getTasks();
        set(state => ({
          tasks: supabaseTasks.map(transformSupabaseTask),
          loading: { ...state.loading, tasks: false }
        }));
      } catch (error) {
        console.error('Failed to sync tasks:', error);
      }
    },

    syncProjects: async () => {
      try {
        const supabaseProjects = await projectService.getProjects();
        set(state => ({
          projects: supabaseProjects.map(transformSupabaseProject),
          loading: { ...state.loading, projects: false }
        }));
      } catch (error) {
        console.error('Failed to sync projects:', error);
      }
    },

    syncUserGroups: async () => {
      try {
        const supabaseUserGroups = await userGroupService.getUserGroups();
        set(state => ({
          userGroups: supabaseUserGroups.map(transformSupabaseUserGroup),
          loading: { ...state.loading, userGroups: false }
        }));
      } catch (error) {
        console.error('Failed to sync user groups:', error);
      }
    },

    // Task actions
    addTask: async (task) => {
      console.log('Adding task:', task.title);
      try {
        const supabaseTask = await taskService.createTask({
          title: task.title,
          description: task.description,
          status: task.status || 'todo',
          priority: task.priority || 'medium',
          assigned_user_id: task.assignedUserId && task.assignedUserId.trim() !== '' ? task.assignedUserId : null,
          assigned_users: task.assignedUsers || [],
          assigned_groups: task.assignedGroups || [],
          owner_id: task.ownerId && task.ownerId.trim() !== '' ? task.ownerId : null,
          due_date: task.dueDate && task.dueDate.trim() !== '' ? task.dueDate : null,
          start_date: task.startDate && task.startDate.trim() !== '' ? task.startDate : null,
          tags: task.tags || [],
          project_id: task.projectId && task.projectId.trim() !== '' ? task.projectId : null,
          folder_id: task.folderId && task.folderId.trim() !== '' ? task.folderId : null,
          effort: task.effort ? JSON.parse(JSON.stringify(task.effort)) : null,
          custom_field_values: task.customFieldValues || {}
        });

        const newTask = transformSupabaseTask(supabaseTask);
        set(state => {
          // Check if task already exists to prevent duplicates
          const existingTask = state.tasks.find(t => t.id === newTask.id);
          if (existingTask) {
            console.log('🔄 Task already exists in state, skipping duplicate add');
            return state;
          }

          return { tasks: [...state.tasks, newTask] };
        });
        console.log('Task added successfully:', newTask.title);

        // Trigger automation for task creation
        try {
          await automationEngine.onTaskCreated(newTask);
        } catch (error) {
          console.warn('Automation trigger failed for task creation:', error);
        }
      } catch (error) {
        console.error('Failed to add task:', error);
        throw error;
      }
    },

    updateTask: async (id, updates) => {
      const timestamp = new Date().toISOString();
      const updateNumber = get().updateCounter + 1;
      set(() => ({ updateCounter: updateNumber }));

      console.log(`[${timestamp}] Update #${updateNumber} - Updating task:`, id, updates);
      console.log(`Update #${updateNumber} - Smart auth listener enabled, ignoring non-essential events`);
      try {
        console.log(`Update #${updateNumber} Step 1: Getting current state...`);
        const state = get();
        console.log(`Update #${updateNumber} - Current state has ${state.tasks.length} tasks`);
        const currentTask = state.tasks.find(task => task.id === id);
        if (!currentTask) {
          console.error(`Update #${updateNumber} - Task not found! Available task IDs:`, state.tasks.map(t => t.id));
          throw new Error('Task not found');
        }
        console.log(`Update #${updateNumber} Step 2: Found current task:`, currentTask.title);

        console.log('Step 3: Skipping user check (already authenticated)...');
        // Skip getCurrentUser() since it's hanging - we know user is authenticated from AuthContext
        console.log('Step 4: User authentication bypassed');

        const supabaseUpdates: any = {};

        if (updates.title !== undefined) supabaseUpdates.title = updates.title;
        if (updates.description !== undefined) supabaseUpdates.description = updates.description;
        if (updates.status !== undefined) supabaseUpdates.status = updates.status;
        if (updates.priority !== undefined) supabaseUpdates.priority = updates.priority;
        // Handle UUID fields - convert empty strings to null
        if (updates.assignedUserId !== undefined) supabaseUpdates.assigned_user_id = updates.assignedUserId === '' ? null : updates.assignedUserId;
        if (updates.assignedUsers !== undefined) supabaseUpdates.assigned_users = updates.assignedUsers;
        if (updates.assignedGroups !== undefined) supabaseUpdates.assigned_groups = updates.assignedGroups;
        if (updates.ownerId !== undefined) supabaseUpdates.owner_id = updates.ownerId === '' ? null : updates.ownerId;
        if (updates.dueDate !== undefined) supabaseUpdates.due_date = updates.dueDate === '' ? null : updates.dueDate;
        if (updates.startDate !== undefined) supabaseUpdates.start_date = updates.startDate === '' ? null : updates.startDate;
        if (updates.tags !== undefined) supabaseUpdates.tags = updates.tags;
        if (updates.projectId !== undefined) supabaseUpdates.project_id = updates.projectId === '' ? null : updates.projectId;
        if (updates.folderId !== undefined) supabaseUpdates.folder_id = updates.folderId === '' ? null : updates.folderId;
        if (updates.effort !== undefined) supabaseUpdates.effort = updates.effort;
        if (updates.customFieldValues !== undefined) {
          // Ensure custom field values is a valid JSON object
          supabaseUpdates.custom_field_values = updates.customFieldValues || {};
          console.log('Setting custom_field_values:', supabaseUpdates.custom_field_values);
        }

        console.log('Step 5: Calling database update with:', supabaseUpdates);

        // If custom field values are causing issues, try without them first
        let updatedTask;
        try {
          // Update task first with timeout protection
          const updatePromise = taskService.updateTask(id, supabaseUpdates);
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Task update timeout')), 10000)
          );

          updatedTask = await Promise.race([updatePromise, timeoutPromise]);
        } catch (error) {
          // If update fails and we have custom field values, try without them
          if (supabaseUpdates.custom_field_values && error.message?.includes('case not found')) {
            console.warn('Custom field values causing database error, retrying without them...');
            const { custom_field_values, ...updatesWithoutCustomFields } = supabaseUpdates;

            const retryPromise = taskService.updateTask(id, updatesWithoutCustomFields);
            const retryTimeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Task update timeout')), 10000)
            );

            updatedTask = await Promise.race([retryPromise, retryTimeoutPromise]);
            console.warn('Task updated successfully without custom field values');
          } else {
            throw error;
          }
        }
        console.log('Step 6: Database update completed:', updatedTask.title);

        // Re-enable history logging with manual user ID (required for time in status tracking)
        console.log('Step 7: Creating history entries...');
        try {
          const historyPromises: Promise<any>[] = [];

          // Get user ID from current auth state instead of getCurrentUser()
          const { data: { user } } = await supabase.auth.getUser();
          const userId = user?.id;

          if (userId) {
            // Helper function to normalize values for comparison (treats null and empty string as equivalent)
            const normalizeValue = (value: any): any => {
              if (value === '' || value === null || value === undefined) {
                return null;
              }
              return value;
            };

            Object.entries(updates).forEach(([field, newValue]) => {
              const oldValue = (currentTask as any)[field];
              const normalizedOldValue = normalizeValue(oldValue);
              const normalizedNewValue = normalizeValue(newValue);

              // Only create history entry if values are actually different after normalization
              if (normalizedNewValue !== normalizedOldValue && newValue !== undefined) {
                historyPromises.push(
                  historyService.addHistoryEntry({
                    task_id: id,
                    user_id: userId,
                    field,
                    old_value: JSON.stringify(normalizedOldValue),
                    new_value: JSON.stringify(normalizedNewValue)
                  })
                );
              }
            });

            // Create history entries in background (don't await to avoid blocking)
            if (historyPromises.length > 0) {
              Promise.all(historyPromises)
                .then(() => {
                  console.log(`✅ Created ${historyPromises.length} history entries for time tracking`);
                })
                .catch(error => {
                  console.warn('❌ Failed to create history entries:', error);
                });
            }
          } else {
            console.warn('No user ID available for history logging');
          }
        } catch (error) {
          console.warn('Failed to create history entries:', error);
        }

        console.log('Step 8: Merging updates with existing task data...');
        // Don't transform the database response - it lacks durations/history
        // Instead, merge the updates with the existing task that has complete data
        const updatedFields = {
          title: updatedTask.title,
          description: updatedTask.description,
          status: updatedTask.status,
          priority: updatedTask.priority,
          assignedUserId: updatedTask.assigned_user_id,
          projectId: updatedTask.project_id,
          folderId: updatedTask.folder_id,
          ownerId: updatedTask.owner_id,
          startDate: updatedTask.start_date,
          dueDate: updatedTask.due_date,
          effort: updatedTask.effort,
          updatedAt: updatedTask.updated_at
        };
        console.log('Step 9: Prepared field updates for:', updatedTask.title);

        // If status changed, we need to refresh durations since database triggers created new ones
        const statusChanged = Object.keys(updates).includes('status');
        if (statusChanged) {
          console.log('Step 10: Status changed, fetching fresh durations...');
          try {
            // Fetch fresh durations from database
            const freshDurations = await durationService.getTaskDurations(id);
            const transformedDurations = freshDurations.map((duration: any) => ({
              status: duration.status,
              startTime: duration.start_time,
              endTime: duration.end_time
            }));

            console.log('Step 11: Fresh durations loaded:', transformedDurations.length);

            // Update state with fresh durations
            set(state => ({
              tasks: state.tasks.map(task =>
                task.id === id
                  ? { ...task, ...updatedFields, durations: transformedDurations }
                  : task
              )
            }));
            console.log('Step 12: Task updated with fresh durations:', updatedTask.title);
          } catch (error) {
            console.warn('Failed to fetch fresh durations, using existing:', error);
            // Fallback to preserving existing durations
            set(state => ({
              tasks: state.tasks.map(task =>
                task.id === id
                  ? { ...task, ...updatedFields }
                  : task
              )
            }));
            console.log('Step 12: Task updated with existing durations (fallback):', updatedTask.title);
          }
        } else {
          console.log('Step 10: No status change, preserving existing durations...');
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === id
                ? { ...task, ...updatedFields }
                : task
            )
          }));
          console.log('Step 11: Task updated with preserved durations:', updatedTask.title);
        }

        // Trigger automation for task update
        try {
          const updatedTaskData = get().tasks.find(task => task.id === id);
          if (updatedTaskData) {
            await automationEngine.onTaskUpdated(currentTask, updatedTaskData);
          }
        } catch (error) {
          console.warn('Automation trigger failed for task update:', error);
        }

        // Handle dependency date propagation if dates changed
        try {
          const dateChanged = (updates.startDate !== undefined && updates.startDate !== currentTask.startDate) ||
                             (updates.dueDate !== undefined && updates.dueDate !== currentTask.dueDate);

          if (dateChanged) {
            console.log('Task dates changed, triggering dependency propagation...');
            await dependencyPropagationService.handleTaskDateChange(
              id,
              currentTask.startDate,
              currentTask.dueDate,
              updates.startDate || currentTask.startDate,
              updates.dueDate || currentTask.dueDate
            );
          }
        } catch (error) {
          console.warn('Dependency propagation failed for task update:', error);
        }
      } catch (error) {
        console.error('Failed to update task:', error);
        throw error;
      }
    },

    moveTask: async (taskId, newStatus) => {
      try {
        await get().updateTask(taskId, { status: newStatus });
      } catch (error) {
        console.error('Failed to move task:', error);
        throw error;
      }
    },

    deleteTask: async (id) => {
      try {
        await taskService.deleteTask(id);
        set(state => ({ tasks: state.tasks.filter(task => task.id !== id) }));
      } catch (error) {
        console.error('Failed to delete task:', error);
        throw error;
      }
    },

    cloneTask: async (taskId, options = {}) => {
      try {
        console.log('Cloning task:', taskId);
        const result = await cloneService.cloneTask(taskId, options);

        if (!result.success) {
          throw new Error(result.error || 'Failed to clone task');
        }

        // Refresh data to get the cloned task
        await get().syncData();

        console.log('Task cloned successfully');
      } catch (error) {
        console.error('Failed to clone task:', error);
        throw error;
      }
    },

    // Comment actions
    addComment: async (taskId, comment) => {
      try {
        const newComment = await commentService.addComment({
          task_id: taskId,
          user_id: comment.userId,
          content: comment.content,
          parent_id: comment.parentId
        });

        const commentForState = {
          id: newComment.id,
          taskId: newComment.task_id,
          userId: newComment.user_id,
          content: newComment.content,
          timestamp: newComment.created_at,
          edited: newComment.edited,
          parentId: newComment.parent_id,
          replies: []
        };

        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  comments: [...task.comments, commentForState]
                }
              : task
          )
        }));

        // Trigger automation for comment added
        try {
          const task = get().tasks.find(t => t.id === taskId);
          if (task) {
            await automationEngine.onCommentAdded(commentForState, task);
          }
        } catch (error) {
          console.warn('Automation trigger failed for comment added:', error);
        }

        return commentForState;
      } catch (error) {
        console.error('Failed to add comment:', error);
        throw error;
      }
    },

    updateComment: async (taskId, commentId, content) => {
      try {
        await commentService.updateComment(commentId, content);

        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  comments: task.comments.map(comment =>
                    comment.id === commentId
                      ? { ...comment, content, edited: true }
                      : comment
                  )
                }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to update comment:', error);
        throw error;
      }
    },

    deleteComment: async (taskId, commentId) => {
      try {
        await commentService.deleteComment(commentId);

        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  comments: task.comments.filter(comment => comment.id !== commentId)
                }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to delete comment:', error);
        throw error;
      }
    },

    // Subtask comment actions
    addSubtaskComment: async (taskId, subtaskId, comment) => {
      try {
        const currentTask = get().tasks.find(task => task.id === taskId);
        if (!currentTask) {
          console.error('Task not found in store. Available tasks:', get().tasks.map(t => ({ id: t.id, title: t.title })));
          console.error('Looking for task ID:', taskId);
          console.warn('Task not found in store - this might be a timing issue with a newly created task');
          throw new Error('Task not found');
        }

        const subtaskIndex = currentTask.subtasks.findIndex(subtask => subtask.id === subtaskId);
        if (subtaskIndex === -1) throw new Error('Subtask not found');

        const newComment = {
          ...comment,
          id: crypto.randomUUID(),
          timestamp: new Date().toISOString(),
          taskId: subtaskId, // For subtask comments, taskId is the subtaskId
          replies: []
        };

        // Update the subtask with the new comment
        const updatedSubtasks = [...currentTask.subtasks];
        updatedSubtasks[subtaskIndex] = {
          ...updatedSubtasks[subtaskIndex],
          comments: [...(updatedSubtasks[subtaskIndex].comments || []), newComment]
        };

        // Update in database
        await taskService.updateTask(taskId, {
          subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
        });

        // Update local state
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, subtasks: updatedSubtasks }
              : task
          )
        }));

        return newComment;
      } catch (error) {
        console.error('Failed to add subtask comment:', error);
        throw error;
      }
    },

    updateSubtaskComment: async (taskId, subtaskId, commentId, content) => {
      try {
        const currentTask = get().tasks.find(task => task.id === taskId);
        if (!currentTask) throw new Error('Task not found');

        const subtaskIndex = currentTask.subtasks.findIndex(subtask => subtask.id === subtaskId);
        if (subtaskIndex === -1) throw new Error('Subtask not found');

        const updatedSubtasks = [...currentTask.subtasks];
        const currentSubtask = updatedSubtasks[subtaskIndex];
        const updatedComments = (currentSubtask.comments || []).map(comment =>
          comment.id === commentId
            ? { ...comment, content, edited: true }
            : comment
        );

        updatedSubtasks[subtaskIndex] = {
          ...currentSubtask,
          comments: updatedComments
        };

        // Update in database
        await taskService.updateTask(taskId, {
          subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
        });

        // Update local state
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, subtasks: updatedSubtasks }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to update subtask comment:', error);
        throw error;
      }
    },

    deleteSubtaskComment: async (taskId, subtaskId, commentId) => {
      try {
        const currentTask = get().tasks.find(task => task.id === taskId);
        if (!currentTask) throw new Error('Task not found');

        const subtaskIndex = currentTask.subtasks.findIndex(subtask => subtask.id === subtaskId);
        if (subtaskIndex === -1) throw new Error('Subtask not found');

        const updatedSubtasks = [...currentTask.subtasks];
        const currentSubtask = updatedSubtasks[subtaskIndex];
        const updatedComments = (currentSubtask.comments || []).filter(comment => comment.id !== commentId);

        updatedSubtasks[subtaskIndex] = {
          ...currentSubtask,
          comments: updatedComments
        };

        // Update in database
        await taskService.updateTask(taskId, {
          subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
        });

        // Update local state
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, subtasks: updatedSubtasks }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to delete subtask comment:', error);
        throw error;
      }
    },

    // Subtask actions
    addSubtask: async (taskId, subtask) => {
      console.log('Adding subtask to task:', taskId, subtask.title);
      try {
          const newSubtask = {
            ...subtask,
            id: crypto.randomUUID(),
            comments: [],
            history: [{
              id: crypto.randomUUID(),
              taskId,
              timestamp: new Date().toISOString(),
              field: 'created',
              oldValue: '',
              newValue: 'Subtask created',
              userId: 'system'
            }],
            durations: [{
              status: subtask.status,
              startTime: new Date().toISOString()
            }]
          };

          // Update the task with the new subtask
          const currentTask = get().tasks.find(task => task.id === taskId);
          if (!currentTask) {
            console.error('Task not found in store. Available tasks:', get().tasks.map(t => ({ id: t.id, title: t.title })));
            console.error('Looking for task ID:', taskId);
            throw new Error('Task not found');
          }

          const updatedSubtasks = [...currentTask.subtasks, newSubtask];

          // Update in database
          await taskService.updateTask(taskId, {
            subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
          });

          // Update local state
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === taskId
                ? { ...task, subtasks: updatedSubtasks }
                : task
            )
          }));
        } catch (error) {
          console.error('Failed to add subtask:', error);
          throw error;
        }
    },

    updateSubtask: async (taskId, subtaskId, updates) => {
      try {
        const currentTask = get().tasks.find(task => task.id === taskId);
        if (!currentTask) {
          console.error('Task not found in store. Available tasks:', get().tasks.map(t => ({ id: t.id, title: t.title })));
          console.error('Looking for task ID:', taskId);
          console.warn('Task not found in store - this might be a timing issue with a newly created task');
          throw new Error('Task not found');
        }

        const updatedSubtasks = currentTask.subtasks.map(subtask =>
          subtask.id === subtaskId ? { ...subtask, ...updates } : subtask
        );

        // Update in database
        await taskService.updateTask(taskId, {
          subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
        });

        // Update local state
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, subtasks: updatedSubtasks }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to update subtask:', error);
        throw error;
      }
    },

    deleteSubtask: async (taskId, subtaskId) => {
      try {
        const currentTask = get().tasks.find(task => task.id === taskId);
        if (!currentTask) throw new Error('Task not found');

        const updatedSubtasks = currentTask.subtasks.filter(subtask => subtask.id !== subtaskId);

        // Update in database
        await taskService.updateTask(taskId, {
          subtasks: JSON.parse(JSON.stringify(updatedSubtasks))
        });

        // Update local state
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, subtasks: updatedSubtasks }
              : task
          )
        }));
      } catch (error) {
        console.error('Failed to delete subtask:', error);
        throw error;
      }
    },

    // Project actions
    addProject: async (project) => {
      try {
        const supabaseProject = await projectService.createProject({
          name: project.name,
          description: project.description,
          color: project.color,
          start_date: project.startDate && project.startDate.trim() !== '' ? project.startDate : null,
          end_date: project.endDate && project.endDate.trim() !== '' ? project.endDate : null,
          folder_id: project.folderId && project.folderId.trim() !== '' ? project.folderId : null,
          effort: project.effort ? JSON.parse(JSON.stringify(project.effort)) : null
        });

        const newProject = transformSupabaseProject(supabaseProject);
        set(state => ({ projects: [...state.projects, newProject] }));

        // Trigger automation for project creation
        try {
          await automationEngine.onProjectCreated(newProject);
        } catch (error) {
          console.warn('Automation trigger failed for project creation:', error);
        }
      } catch (error) {
        console.error('Failed to add project:', error);
        throw error;
      }
    },

    updateProject: async (id, updates) => {
      try {
        const supabaseUpdates: any = {};

        if (updates.name !== undefined) supabaseUpdates.name = updates.name;
        if (updates.description !== undefined) supabaseUpdates.description = updates.description;
        if (updates.color !== undefined) supabaseUpdates.color = updates.color;
        if (updates.startDate !== undefined) supabaseUpdates.start_date = updates.startDate;
        if (updates.endDate !== undefined) supabaseUpdates.end_date = updates.endDate;
        // Handle UUID fields - convert empty strings to null
        if (updates.folderId !== undefined) supabaseUpdates.folder_id = updates.folderId === '' ? null : updates.folderId;
        if (updates.effort !== undefined) supabaseUpdates.effort = updates.effort;

        const updatedProject = await projectService.updateProject(id, supabaseUpdates);
        const transformedProject = transformSupabaseProject(updatedProject);

        const currentProject = get().projects.find(p => p.id === id);

        set(state => ({
          projects: state.projects.map(project =>
            project.id === id ? { ...project, ...transformedProject } : project
          )
        }));

        // Trigger automation for project update
        try {
          const updatedProjectData = get().projects.find(p => p.id === id);
          if (currentProject && updatedProjectData) {
            await automationEngine.onProjectUpdated(currentProject, updatedProjectData);
          }
        } catch (error) {
          console.warn('Automation trigger failed for project update:', error);
        }
      } catch (error) {
        console.error('Failed to update project:', error);
        throw error;
      }
    },

    deleteProject: async (id) => {
      try {
        await projectService.deleteProject(id);
        set(state => ({
          projects: state.projects.filter(project => project.id !== id),
          tasks: state.tasks.filter(task => task.projectId !== id)
        }));
      } catch (error) {
        console.error('Failed to delete project:', error);
        throw error;
      }
    },

    cloneProject: async (projectId, options = {}) => {
      try {
        console.log('Cloning project:', projectId);
        const result = await cloneService.cloneProject(projectId, options);

        if (!result.success) {
          throw new Error(result.error || 'Failed to clone project');
        }

        // Refresh data to get the cloned project and tasks
        await get().syncData();

        console.log('Project cloned successfully');
      } catch (error) {
        console.error('Failed to clone project:', error);
        throw error;
      }
    },

    // User Group actions
    addUserGroup: async (group) => {
      try {
        const supabaseGroup = await userGroupService.createUserGroup({
          name: group.name,
          color: group.color
        });

        const newGroup = transformSupabaseUserGroup(supabaseGroup);
        set(state => ({ userGroups: [...state.userGroups, newGroup] }));
      } catch (error) {
        console.error('Failed to add user group:', error);
        throw error;
      }
    },

    updateUserGroup: async (id, updates) => {
      try {
        const updatedGroup = await userGroupService.updateUserGroup(id, updates);
        const transformedGroup = transformSupabaseUserGroup(updatedGroup);

        set(state => ({
          userGroups: state.userGroups.map(group =>
            group.id === id ? { ...group, ...transformedGroup } : group
          )
        }));
      } catch (error) {
        console.error('Failed to update user group:', error);
        throw error;
      }
    },

    deleteUserGroup: async (id) => {
      try {
        await userGroupService.deleteUserGroup(id);
        set(state => ({ userGroups: state.userGroups.filter(group => group.id !== id) }));
      } catch (error) {
        console.error('Failed to delete user group:', error);
        throw error;
      }
    },

    // Column actions
    addColumn: async (column) => {
      try {
        const supabaseColumn = await columnService.createColumn({
          title: column.title,
          color: column.color,
          position: get().columns.length
        });

        // Don't add to local state here - let real-time handle it
        // This prevents duplication when real-time event fires
        console.log('✅ Column created in database, waiting for real-time sync...');
      } catch (error) {
        console.error('Failed to add column:', error);
        throw error;
      }
    },

    updateColumn: async (id, updates) => {
      try {
        // Find the column to get the database ID
        const column = get().columns.find(col => col.id === id);
        const dbId = column?.dbId || id; // Use dbId if available, fallback to id

        const updatedColumn = await columnService.updateColumn(dbId, updates);
        const transformedColumn = transformSupabaseColumn(updatedColumn);

        set(state => ({
          columns: state.columns.map(column =>
            column.id === id ? { ...column, ...transformedColumn } : column
          )
        }));
      } catch (error) {
        console.error('Failed to update column:', error);
        throw error;
      }
    },

    deleteColumn: async (id) => {
      try {
        // Find the column to get the database ID
        const column = get().columns.find(col => col.id === id);
        const dbId = column?.dbId || id; // Use dbId if available, fallback to id

        await columnService.deleteColumn(dbId);
        set(state => ({ columns: state.columns.filter(column => column.id !== id) }));
      } catch (error) {
        console.error('Failed to delete column:', error);
        throw error;
      }
    },

    reorderColumns: async (columns) => {
      try {
        // Update positions in Supabase using database IDs
        await Promise.all(
          columns.map((column, index) => {
            const dbId = column.dbId || column.id;
            return columnService.updateColumn(dbId, { position: index });
          })
        );

        set({ columns });
      } catch (error) {
        console.error('Failed to reorder columns:', error);
        throw error;
      }
    },

    // Folder actions
    addFolder: async (folder) => {
      try {
        const supabaseFolder = await folderService.createFolder({
          name: folder.name,
          parent_id: folder.parentId
        });

        const newFolder = transformSupabaseFolder(supabaseFolder);
        set(state => ({ folders: [...state.folders, newFolder] }));
      } catch (error) {
        console.error('Failed to add folder:', error);
        throw error;
      }
    },

    updateFolder: async (id, updates) => {
      try {
        const supabaseUpdates: any = {};
        if (updates.name !== undefined) supabaseUpdates.name = updates.name;
        if (updates.parentId !== undefined) supabaseUpdates.parent_id = updates.parentId;

        const updatedFolder = await folderService.updateFolder(id, supabaseUpdates);
        const transformedFolder = transformSupabaseFolder(updatedFolder);

        set(state => ({
          folders: state.folders.map(folder =>
            folder.id === id ? { ...folder, ...transformedFolder } : folder
          )
        }));
      } catch (error) {
        console.error('Failed to update folder:', error);
        throw error;
      }
    },

    deleteFolder: async (id) => {
      try {
        await folderService.deleteFolder(id);
        set(state => ({ folders: state.folders.filter(folder => folder.id !== id) }));
      } catch (error) {
        console.error('Failed to delete folder:', error);
        throw error;
      }
    },

    // User actions (simplified - mainly for display)
    addUser: async (user) => {
      try {
        const supabaseUser = await userProfileService.createProfile({
          id: user.id || crypto.randomUUID(),
          email: user.email,
          name: user.name,
          group_id: user.groupId,
          avatar_url: user.avatar,
          skillset_ids: user.skillsetIds || []
        });

        const newUser = transformSupabaseUser(supabaseUser);
        set(state => ({ users: [...state.users, newUser] }));
      } catch (error) {
        console.error('Failed to add user:', error);
        throw error;
      }
    },

    updateUser: async (id, updates) => {
      try {
        const supabaseUpdates: any = {};
        if (updates.name !== undefined) supabaseUpdates.name = updates.name;
        if (updates.email !== undefined) supabaseUpdates.email = updates.email;
        if (updates.groupId !== undefined) {
          // Handle empty string as null for group_id
          supabaseUpdates.group_id = updates.groupId === '' ? null : updates.groupId;
        }
        if (updates.avatar !== undefined) supabaseUpdates.avatar_url = updates.avatar;
        if (updates.skillsetIds !== undefined) {
          // Ensure skillset_ids is properly formatted as UUID array
          supabaseUpdates.skillset_ids = updates.skillsetIds || [];
        }

        const updatedUser = await userProfileService.updateProfile(id, supabaseUpdates);
        const transformedUser = transformSupabaseUser(updatedUser);

        set(state => ({
          users: state.users.map(user =>
            user.id === id ? { ...user, ...transformedUser } : user
          )
        }));
      } catch (error) {
        console.error('Failed to update user:', error);
        throw error;
      }
    },

    deleteUser: async (id) => {
      try {
        // Note: In a real app, you might not want to delete users, just deactivate them
        set(state => ({ users: state.users.filter(user => user.id !== id) }));
      } catch (error) {
        console.error('Failed to delete user:', error);
        throw error;
      }
    },

    // Skillset actions
    addSkillsetGroup: async (skillset) => {
      try {
        const supabaseSkillset = await skillsetService.createSkillsetGroup({
          name: skillset.name,
          description: skillset.description,
          color: skillset.color
        });

        const newSkillset = transformSupabaseSkillset(supabaseSkillset);
        set(state => ({ skillsetGroups: [...state.skillsetGroups, newSkillset] }));
      } catch (error) {
        console.error('Failed to add skillset group:', error);
        throw error;
      }
    },

    updateSkillsetGroup: async (id, updates) => {
      try {
        const updatedSkillset = await skillsetService.updateSkillsetGroup(id, updates);
        const transformedSkillset = transformSupabaseSkillset(updatedSkillset);

        set(state => ({
          skillsetGroups: state.skillsetGroups.map(skillset =>
            skillset.id === id ? { ...skillset, ...transformedSkillset } : skillset
          )
        }));
      } catch (error) {
        console.error('Failed to update skillset group:', error);
        throw error;
      }
    },

    deleteSkillsetGroup: async (id) => {
      try {
        await skillsetService.deleteSkillsetGroup(id);
        set(state => ({
          skillsetGroups: state.skillsetGroups.filter(skillset => skillset.id !== id),
          users: state.users.map(user => ({
            ...user,
            skillsetIds: user.skillsetIds.filter(skillsetId => skillsetId !== id)
          }))
        }));
      } catch (error) {
        console.error('Failed to delete skillset group:', error);
        throw error;
      }
    },

    // Capacity actions
    addUserCapacity: async (capacity) => {
      try {
        const supabaseCapacity = await capacityService.addUserCapacity({
          user_id: capacity.userId,
          daily_hours: capacity.dailyHours,
          weekly_hours: capacity.weeklyHours,
          working_days: capacity.workingDays,
          effective_from: capacity.effectiveFrom,
          effective_to: capacity.effectiveTo
        });

        const newCapacity = transformSupabaseCapacity(supabaseCapacity);
        set(state => ({
          userCapacities: [...state.userCapacities.filter(c => c.userId !== capacity.userId), newCapacity]
        }));
      } catch (error) {
        console.error('Failed to add user capacity:', error);
        throw error;
      }
    },

    updateUserCapacity: async (id, updates) => {
      try {
        const supabaseUpdates: any = {};
        if (updates.dailyHours !== undefined) supabaseUpdates.daily_hours = updates.dailyHours;
        if (updates.weeklyHours !== undefined) supabaseUpdates.weekly_hours = updates.weeklyHours;
        if (updates.workingDays !== undefined) supabaseUpdates.working_days = updates.workingDays;
        if (updates.effectiveFrom !== undefined) supabaseUpdates.effective_from = updates.effectiveFrom;
        if (updates.effectiveTo !== undefined) supabaseUpdates.effective_to = updates.effectiveTo;

        const updatedCapacity = await capacityService.updateUserCapacity(id, supabaseUpdates);
        const transformedCapacity = transformSupabaseCapacity(updatedCapacity);

        set(state => ({
          userCapacities: state.userCapacities.map(capacity =>
            capacity.id === transformedCapacity.id ? transformedCapacity : capacity
          )
        }));
      } catch (error) {
        console.error('Failed to update user capacity:', error);
        throw error;
      }
    },

    deleteUserCapacity: async (id) => {
      try {
        await capacityService.deleteUserCapacity(id);
        // Note: We need to find by id, not userId for deletion
        set(state => ({ userCapacities: state.userCapacities.filter((_, index) => index.toString() !== id) }));
      } catch (error) {
        console.error('Failed to delete user capacity:', error);
        throw error;
      }
    },

    // Task Effort actions
    addTaskEffort: async (effort) => {
      try {
        const supabaseEffort = await effortService.addTaskEffort({
          task_id: effort.taskId,
          estimated_hours: effort.estimatedHours,
          actual_hours: effort.actualHours,
          assigned_user_id: effort.assignedUserId,
          required_skillsets: effort.requiredSkillsets
        });

        const newEffort = transformSupabaseEffort(supabaseEffort);
        set(state => ({ taskEfforts: [...state.taskEfforts, newEffort] }));
      } catch (error) {
        console.error('Failed to add task effort:', error);
        throw error;
      }
    },

    updateTaskEffort: async (id, updates) => {
      try {
        const supabaseUpdates: any = {};
        if (updates.estimatedHours !== undefined) supabaseUpdates.estimated_hours = updates.estimatedHours;
        if (updates.actualHours !== undefined) supabaseUpdates.actual_hours = updates.actualHours;
        if (updates.assignedUserId !== undefined) supabaseUpdates.assigned_user_id = updates.assignedUserId;
        if (updates.requiredSkillsets !== undefined) supabaseUpdates.required_skillsets = updates.requiredSkillsets;

        const updatedEffort = await effortService.updateTaskEffort(id, supabaseUpdates);
        const transformedEffort = transformSupabaseEffort(updatedEffort);

        set(state => ({
          taskEfforts: state.taskEfforts.map(effort =>
            effort.taskId === transformedEffort.taskId ? transformedEffort : effort
          )
        }));
      } catch (error) {
        console.error('Failed to update task effort:', error);
        throw error;
      }
    },

    deleteTaskEffort: async (id) => {
      try {
        await effortService.deleteTaskEffort(id);
        set(state => ({ taskEfforts: state.taskEfforts.filter((_, index) => index.toString() !== id) }));
      } catch (error) {
        console.error('Failed to delete task effort:', error);
        throw error;
      }
    },

    // UI actions
    setTasksViewMode: (mode) => set({ tasksViewMode: mode }),

    setSelectedTreeNode: (nodeId) => set({ selectedTreeNode: nodeId }),

    toggleTreeNode: (nodeId) => set((state) => {
      const newExpanded = new Set(state.expandedTreeNodes);
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId);
      } else {
        newExpanded.add(nodeId);
      }
      return { expandedTreeNodes: newExpanded };
    }),

    setTaskFilters: (filters) => set((state) => ({
      taskFilters: { ...state.taskFilters, ...filters }
    })),

    clearTaskFilters: () => set({
      taskFilters: {
        assignedUsers: [],
        status: [],
        priority: [],
        owners: [],
        assignedGroups: [],
        tags: [],
        dueDateRange: {},
        hasOverdueTasks: false,
        search: undefined,
      }
    }),

    // Real-time subscriptions for multi-user collaboration
    setupRealtimeSubscriptions: () => {
      const state = get();

      // Prevent too many reconnection attempts
      if (state.reconnectAttempts >= state.maxReconnectAttempts) {
        console.log('❌ Max reconnection attempts reached, enabling polling fallback');
        get().startPolling();
        return;
      }

      // Implement exponential backoff for reconnections
      if (state.reconnectAttempts > 0) {
        const backoffDelay = Math.min(1000 * Math.pow(2, state.reconnectAttempts), 30000);
        console.log(`⏳ Waiting ${backoffDelay}ms before reconnection attempt ${state.reconnectAttempts + 1}`);
        setTimeout(() => {
          get().attemptRealtimeConnection();
        }, backoffDelay);
        return;
      }

      get().attemptRealtimeConnection();
    },

    attemptRealtimeConnection: () => {
      set(state => ({
        connectionState: 'connecting',
        reconnectAttempts: state.reconnectAttempts + 1,
        lastConnectionTime: Date.now()
      }));

      // Clean up existing channels properly
      const existingChannels = supabase.getChannels();
      if (existingChannels.length > 0) {
        console.log('Cleaning up existing channels:', existingChannels.length);
        existingChannels.forEach(channel => {
          try {
            channel.unsubscribe();
          } catch (error) {
            console.warn('Error unsubscribing channel:', error);
          }
        });
        supabase.removeAllChannels();
      }

      console.log('Setting up real-time subscriptions for multi-user sync...');

      // Subscribe to tasks table changes with proper error handling
      const tasksChannel = supabase
        .channel('tasks-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'tasks' },
          (payload) => {
            console.log('🔄 Task change detected:', payload);
            get().handleTaskChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Tasks channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Tasks real-time subscription active');
            get().onConnectionSuccess();
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Tasks subscription failed, enabling polling fallback');
            get().onConnectionError();
          }
        });

      // Subscribe to projects table changes
      const projectsChannel = supabase
        .channel('projects-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'projects' },
          (payload) => {
            console.log('🔄 Project change detected:', payload);
            get().handleProjectChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Projects channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Projects real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Projects subscription failed');
          }
        });

      // Subscribe to kanban columns changes
      const columnsChannel = supabase
        .channel('columns-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'kanban_columns' },
          (payload) => {
            console.log('🔄 Column change detected:', payload);
            get().handleColumnChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Columns channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Columns real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Columns subscription failed');
          }
        });

      // Subscribe to folders changes
      const foldersChannel = supabase
        .channel('folders-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'folders' },
          (payload) => {
            console.log('🔄 Folder change detected:', payload);
            get().handleFolderChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Folders channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Folders real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Folders subscription failed');
          }
        });

      // Subscribe to user profiles changes (skillsets, roles, etc.)
      const userProfilesChannel = supabase
        .channel('user-profiles-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'user_profiles' },
          (payload) => {
            console.log('🔄 User profile change detected:', payload);
            get().handleUserProfileChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 User profiles channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ User profiles real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ User profiles subscription failed');
          }
        });

      // Subscribe to skillset groups changes
      const skillsetsChannel = supabase
        .channel('skillsets-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'skillset_groups' },
          (payload) => {
            console.log('🔄 Skillset change detected:', payload);
            get().handleSkillsetChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Skillsets channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Skillsets real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Skillsets subscription failed');
          }
        });

      // Subscribe to user capacities changes
      const capacitiesChannel = supabase
        .channel('capacities-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'user_capacities' },
          (payload) => {
            console.log('🔄 Capacity change detected:', payload);
            get().handleCapacityChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Capacities channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Capacities real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Capacities subscription failed');
          }
        });

      // Subscribe to notifications changes
      const notificationsChannel = supabase
        .channel('notifications-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'notifications' },
          (payload) => {
            console.log('🔄 Notification change detected:', payload);
            get().handleNotificationChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Notifications channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Notifications real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Notifications subscription failed');
          }
        });

      // Subscribe to task comments changes
      const commentsChannel = supabase
        .channel('comments-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'task_comments' },
          (payload) => {
            console.log('🔄 Comment change detected:', payload);
            get().handleCommentChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Comments channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Comments real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Comments subscription failed');
          }
        });

      // Subscribe to task history changes
      const historyChannel = supabase
        .channel('history-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'task_history' },
          (payload) => {
            console.log('🔄 History change detected:', payload);
            get().handleHistoryChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 History channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ History real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ History subscription failed');
          }
        });

      // Subscribe to task dependencies changes
      const dependenciesChannel = supabase
        .channel('dependencies-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'task_dependencies' },
          (payload) => {
            console.log('🔄 Dependency change detected:', payload);
            get().handleDependencyChange(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Dependencies channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Dependencies real-time subscription active');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Dependencies subscription failed');
          }
        });

      // Set up a timeout to enable polling if subscriptions don't work
      setTimeout(() => {
        const channels = supabase.getChannels();
        const subscribedChannels = channels.filter(ch => ch.state === 'joined');

        console.log(`📊 Subscription status: ${subscribedChannels.length}/${channels.length} channels active`);

        if (subscribedChannels.length < 10) { // We expect 10 channels (tasks, projects, columns, folders, users, skillsets, capacities, notifications, comments, history)
          console.log('⚠️ Some real-time subscriptions failed, enabling polling fallback');
          get().startPolling();
        } else {
          console.log('✅ All real-time subscriptions working, polling disabled');
          get().stopPolling();
        }
      }, 5000); // Check after 5 seconds

      // Also add a periodic check every 30 seconds to ensure real-time is working
      setInterval(() => {
        const channels = supabase.getChannels();
        const activeChannels = channels.filter(ch => ch.state === 'joined');
        const state = get();

        if (activeChannels.length < 10 && state.connectionState !== 'connecting') {
          console.log('🔄 Real-time connection degraded, refreshing subscriptions...');
          // Only attempt reconnection if we haven't exceeded max attempts recently
          const timeSinceLastAttempt = Date.now() - state.lastConnectionTime;
          if (timeSinceLastAttempt > 60000) { // Reset attempts after 1 minute
            set({ reconnectAttempts: 0 });
          }
          get().setupRealtimeSubscriptions();
        }
      }, 30000);

      console.log('Real-time subscriptions setup completed');
    },

    // Handle successful real-time connection
    onConnectionSuccess: () => {
      set({
        connectionState: 'connected',
        reconnectAttempts: 0,
        lastConnectionTime: Date.now()
      });

      // Stop polling if it was enabled
      get().stopPolling();
      console.log('✅ Real-time connection established successfully');
    },

    // Handle real-time connection errors
    onConnectionError: () => {
      set(state => ({
        connectionState: 'error',
        reconnectAttempts: state.reconnectAttempts
      }));

      const state = get();
      if (state.reconnectAttempts < state.maxReconnectAttempts) {
        console.log(`🔄 Connection failed, will retry (${state.reconnectAttempts}/${state.maxReconnectAttempts})`);
        // Retry with exponential backoff
        setTimeout(() => {
          get().setupRealtimeSubscriptions();
        }, 2000);
      } else {
        console.log('❌ Max reconnection attempts reached, enabling polling fallback');
        get().startPolling();
      }
    },

    // Real-time change handlers
    handleTaskChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      // Get current user to ignore own changes
      supabase.auth.getUser().then(async ({ data: { user } }) => {
        // Add a small delay to prevent immediate conflicts with ongoing updates
        await new Promise(resolve => setTimeout(resolve, 300));

        // For real-time updates, we process all changes to keep everyone in sync
        // This ensures version numbers and data stay consistent across users
        console.log(`🔄 Processing ${eventType} for task:`, newRecord?.title || oldRecord?.title);

        switch (eventType) {
          case 'INSERT': {
            // Check if task already exists to prevent duplicates
            const currentState = get();
            const existingTask = currentState.tasks.find(t => t.id === newRecord.id);

            if (existingTask) {
              console.log('🔄 Task already exists, skipping duplicate INSERT:', newRecord.title);
              break;
            }

            // For new tasks, fetch full data including durations and history
            try {
              const fullTaskData = await taskService.getTasks();
              const newTask = fullTaskData.find(t => t.id === newRecord.id);
              if (newTask) {
                const transformedTask = transformSupabaseTask(newTask);
                set(state => {
                  // Double-check to prevent race conditions
                  const stillExists = state.tasks.find(t => t.id === transformedTask.id);
                  if (stillExists) {
                    console.log('🔄 Task already exists in state, skipping duplicate');
                    return state;
                  }

                  return {
                    tasks: [...state.tasks, transformedTask],
                    updateCounter: state.updateCounter + 1
                  };
                });
                console.log('✅ New task added from real-time:', transformedTask.title);
              }
            } catch (error) {
              console.error('❌ Failed to fetch full task data for INSERT:', error);
              // Fallback to basic transformation with duplicate check
              const newTask = transformSupabaseTask(newRecord);
              set(state => {
                const stillExists = state.tasks.find(t => t.id === newTask.id);
                if (stillExists) {
                  console.log('🔄 Task already exists in state, skipping duplicate fallback');
                  return state;
                }

                return {
                  tasks: [...state.tasks, newTask],
                  updateCounter: state.updateCounter + 1
                };
              });
            }
            break;
          }

          case 'UPDATE': {
            // For updates, use the real-time data directly to avoid version conflicts
            try {
              const updatedTask = transformSupabaseTask(newRecord);
              set(state => ({
                tasks: state.tasks.map(task =>
                  task.id === updatedTask.id ? {
                    ...task,
                    ...updatedTask,
                    // Preserve local durations and history if they exist and are newer
                    durations: task.durations && task.durations.length > 0 ? task.durations : updatedTask.durations,
                    history: task.history && task.history.length > 0 ? task.history : updatedTask.history
                  } : task
                ),
                updateCounter: state.updateCounter + 1
              }));
              console.log('✅ Task updated from real-time:', updatedTask.title);
            } catch (error) {
              console.error('❌ Failed to process real-time task update:', error);
            }
            break;
          }

          case 'DELETE': {
            set(state => ({
              tasks: state.tasks.filter(task => task.id !== oldRecord.id),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Task deleted from real-time:', oldRecord.title);
            break;
          }
        }
      });
    },

    handleProjectChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      supabase.auth.getUser().then(({ data: { user } }) => {
        // For INSERT operations, check created_by; for UPDATE operations, check updated_by
        const isOwnChange = eventType === 'INSERT'
          ? newRecord?.created_by === user?.id
          : newRecord?.updated_by === user?.id;

        if (isOwnChange) {
          console.log('Ignoring own project change to prevent double update');
          return;
        }

        console.log(`Processing ${eventType} for project:`, newRecord || oldRecord);

        switch (eventType) {
          case 'INSERT': {
            const newProject = transformSupabaseProject(newRecord);
            set(state => ({
              projects: [...state.projects, newProject],
              updateCounter: state.updateCounter + 1
            }));
            break;
          }

          case 'UPDATE': {
            const updatedProject = transformSupabaseProject(newRecord);
            set(state => ({
              projects: state.projects.map(project =>
                project.id === updatedProject.id ? updatedProject : project
              ),
              updateCounter: state.updateCounter + 1
            }));
            break;
          }

          case 'DELETE': {
            set(state => ({
              projects: state.projects.filter(project => project.id !== oldRecord.id),
              updateCounter: state.updateCounter + 1
            }));
            break;
          }
        }
      });
    },

    handleColumnChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      supabase.auth.getUser().then(({ data: { user } }) => {
        // For columns, we process all changes (including our own) to ensure consistency
        // This is because columns are simpler entities without complex relationships

        console.log(`🔄 Processing ${eventType} for column:`, newRecord?.title || oldRecord?.title);

        switch (eventType) {
          case 'INSERT': {
            const newColumn = transformSupabaseColumn(newRecord);
            set(state => {
              // Check if column already exists to prevent duplicates
              const exists = state.columns.some(col => col.id === newColumn.id);
              if (exists) {
                console.log('🔄 Column already exists, skipping duplicate');
                return state;
              }

              return {
                columns: [...state.columns, newColumn],
                updateCounter: state.updateCounter + 1
              };
            });
            console.log('✅ New column added from real-time:', newColumn.title);
            break;
          }

          case 'UPDATE': {
            const updatedColumn = transformSupabaseColumn(newRecord);
            set(state => ({
              columns: state.columns.map(column =>
                column.dbId === newRecord.id ? updatedColumn : column
              ),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Column updated from real-time:', updatedColumn.title);
            break;
          }

          case 'DELETE': {
            set(state => ({
              columns: state.columns.filter(column => column.dbId !== oldRecord.id),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Column deleted from real-time:', oldRecord.title);
            break;
          }
        }
      });
    },

    handleFolderChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      supabase.auth.getUser().then(({ data: { user } }) => {
        // For INSERT operations, check created_by; for UPDATE operations, check updated_by
        const isOwnChange = eventType === 'INSERT'
          ? newRecord?.created_by === user?.id
          : newRecord?.updated_by === user?.id;

        if (isOwnChange) {
          console.log('Ignoring own folder change to prevent double update');
          return;
        }

        console.log(`Processing ${eventType} for folder:`, newRecord || oldRecord);

        switch (eventType) {
          case 'INSERT': {
            const newFolder = transformSupabaseFolder(newRecord);
            set(state => ({
              folders: [...state.folders, newFolder],
              updateCounter: state.updateCounter + 1
            }));
            break;
          }

          case 'UPDATE': {
            const updatedFolder = transformSupabaseFolder(newRecord);
            set(state => ({
              folders: state.folders.map(folder =>
                folder.id === updatedFolder.id ? updatedFolder : folder
              ),
              updateCounter: state.updateCounter + 1
            }));
            break;
          }

          case 'DELETE': {
            set(state => ({
              folders: state.folders.filter(folder => folder.id !== oldRecord.id),
              updateCounter: state.updateCounter + 1
            }));
            break;
          }
        }
      });
    },

    handleUserProfileChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for user profile:`, newRecord?.name || oldRecord?.name);

      switch (eventType) {
        case 'INSERT': {
          const newUser = transformSupabaseUser(newRecord);
          set(state => ({
            users: [...state.users, newUser],
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ New user added from real-time:', newUser.name);
          break;
        }

        case 'UPDATE': {
          const updatedUser = transformSupabaseUser(newRecord);
          set(state => ({
            users: state.users.map(user =>
              user.id === updatedUser.id ? updatedUser : user
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ User profile updated from real-time:', updatedUser.name);
          break;
        }

        case 'DELETE': {
          set(state => ({
            users: state.users.filter(user => user.id !== oldRecord.id),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ User deleted from real-time:', oldRecord.name);
          break;
        }
      }
    },

    handleSkillsetChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for skillset:`, newRecord?.name || oldRecord?.name);

      switch (eventType) {
        case 'INSERT': {
          const newSkillset = transformSupabaseSkillset(newRecord);
          set(state => ({
            skillsets: [...state.skillsets, newSkillset],
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ New skillset added from real-time:', newSkillset.name);
          break;
        }

        case 'UPDATE': {
          const updatedSkillset = transformSupabaseSkillset(newRecord);
          set(state => ({
            skillsets: state.skillsets.map(skillset =>
              skillset.id === updatedSkillset.id ? updatedSkillset : skillset
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Skillset updated from real-time:', updatedSkillset.name);
          break;
        }

        case 'DELETE': {
          set(state => ({
            skillsets: state.skillsets.filter(skillset => skillset.id !== oldRecord.id),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Skillset deleted from real-time:', oldRecord.name);
          break;
        }
      }
    },

    handleCapacityChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for capacity:`, newRecord?.user_id || oldRecord?.user_id);

      switch (eventType) {
        case 'INSERT': {
          const newCapacity = transformSupabaseCapacity(newRecord);
          set(state => ({
            userCapacities: [...state.userCapacities, newCapacity],
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ New capacity added from real-time');
          break;
        }

        case 'UPDATE': {
          const updatedCapacity = transformSupabaseCapacity(newRecord);
          set(state => ({
            userCapacities: state.userCapacities.map(capacity =>
              capacity.id === updatedCapacity.id ? updatedCapacity : capacity
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Capacity updated from real-time');
          break;
        }

        case 'DELETE': {
          set(state => ({
            userCapacities: state.userCapacities.filter(capacity => capacity.id !== oldRecord.id),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Capacity deleted from real-time');
          break;
        }
      }
    },

    handleNotificationChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for notification:`, newRecord?.id || oldRecord?.id);

      // Get current user to filter notifications
      supabase.auth.getUser().then(({ data: { user } }) => {
        if (!user) return;

        switch (eventType) {
          case 'INSERT': {
            // Only add notification if it's for the current user
            if (newRecord.recipient_id === user.id) {
              const transformedNotification: Notification = {
                id: newRecord.id,
                recipientId: newRecord.recipient_id,
                senderId: newRecord.sender_id,
                type: newRecord.type,
                title: newRecord.title,
                content: newRecord.content,
                taskId: newRecord.task_id || undefined,
                commentId: newRecord.comment_id || undefined,
                isRead: newRecord.is_read,
                createdAt: newRecord.created_at,
                updatedAt: newRecord.updated_at
              };

              set(state => ({
                notifications: [transformedNotification, ...state.notifications],
                updateCounter: state.updateCounter + 1
              }));
              console.log('✅ Notification added from real-time');
            }
            break;
          }

          case 'UPDATE': {
            // Only update notification if it's for the current user
            if (newRecord.recipient_id === user.id) {
              set(state => ({
                notifications: state.notifications.map(notification =>
                  notification.id === newRecord.id
                    ? {
                        ...notification,
                        isRead: newRecord.is_read,
                        updatedAt: newRecord.updated_at
                      }
                    : notification
                ),
                updateCounter: state.updateCounter + 1
              }));
              console.log('✅ Notification updated from real-time');
            }
            break;
          }

          case 'DELETE': {
            set(state => ({
              notifications: state.notifications.filter(notification => notification.id !== oldRecord.id),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Notification deleted from real-time');
            break;
          }
        }
      });
    },

    handleCommentChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for comment:`, newRecord?.id || oldRecord?.id);

      switch (eventType) {
        case 'INSERT': {
          const transformedComment: TaskComment = {
            id: newRecord.id,
            taskId: newRecord.task_id,
            userId: newRecord.user_id,
            content: newRecord.content,
            timestamp: newRecord.created_at,
            edited: newRecord.edited || false,
            parentId: newRecord.parent_id || undefined,
            replies: []
          };

          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === newRecord.task_id
                ? {
                    ...task,
                    comments: [...task.comments, transformedComment]
                  }
                : task
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Comment added from real-time');
          break;
        }

        case 'UPDATE': {
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === newRecord.task_id
                ? {
                    ...task,
                    comments: task.comments.map(comment =>
                      comment.id === newRecord.id
                        ? {
                            ...comment,
                            content: newRecord.content,
                            edited: newRecord.edited,
                            timestamp: newRecord.updated_at
                          }
                        : comment
                    )
                  }
                : task
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Comment updated from real-time');
          break;
        }

        case 'DELETE': {
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === oldRecord.task_id
                ? {
                    ...task,
                    comments: task.comments.filter(comment => comment.id !== oldRecord.id)
                  }
                : task
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ Comment deleted from real-time');
          break;
        }
      }
    },

    handleHistoryChange: (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      console.log(`🔄 Processing ${eventType} for history:`, newRecord?.id || oldRecord?.id);
      console.log('📝 History payload details:', { eventType, newRecord, oldRecord });

      switch (eventType) {
        case 'INSERT': {
          const transformedHistoryEntry: TaskHistoryEntry = {
            id: newRecord.id,
            taskId: newRecord.task_id,
            timestamp: newRecord.created_at,
            field: newRecord.field,
            oldValue: newRecord.old_value,
            newValue: newRecord.new_value,
            userId: newRecord.user_id
          };

          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === newRecord.task_id
                ? {
                    ...task,
                    history: [...task.history, transformedHistoryEntry]
                  }
                : task
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ History entry added from real-time');
          break;
        }

        case 'DELETE': {
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === oldRecord.task_id
                ? {
                    ...task,
                    history: task.history.filter(entry => entry.id !== oldRecord.id)
                  }
                : task
            ),
            updateCounter: state.updateCounter + 1
          }));
          console.log('✅ History entry deleted from real-time');
          break;
        }
      }
    },

    handleDependencyChange: (payload: any) => {
      const { eventType, new: newDependency, old: oldDependency } = payload;
      console.log('🔄 Processing dependency change:', { eventType, newDependency, oldDependency });

      switch (eventType) {
        case 'INSERT':
          if (newDependency) {
            const transformedDependency = {
              id: newDependency.id,
              predecessorTaskId: newDependency.predecessor_task_id,
              successorTaskId: newDependency.successor_task_id,
              dependencyType: newDependency.dependency_type,
              lagDays: newDependency.lag_days || 0,
              createdAt: newDependency.created_at,
              updatedAt: newDependency.updated_at,
              createdBy: newDependency.created_by
            };

            set(state => ({
              taskDependencies: [...state.taskDependencies, transformedDependency],
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Dependency added to store:', transformedDependency);
          }
          break;

        case 'UPDATE':
          if (newDependency) {
            const transformedDependency = {
              id: newDependency.id,
              predecessorTaskId: newDependency.predecessor_task_id,
              successorTaskId: newDependency.successor_task_id,
              dependencyType: newDependency.dependency_type,
              lagDays: newDependency.lag_days || 0,
              createdAt: newDependency.created_at,
              updatedAt: newDependency.updated_at,
              createdBy: newDependency.created_by
            };

            set(state => ({
              taskDependencies: state.taskDependencies.map(dep =>
                dep.id === transformedDependency.id ? transformedDependency : dep
              ),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Dependency updated in store:', transformedDependency);
          }
          break;

        case 'DELETE':
          if (oldDependency) {
            set(state => ({
              taskDependencies: state.taskDependencies.filter(dep => dep.id !== oldDependency.id),
              updateCounter: state.updateCounter + 1
            }));
            console.log('✅ Dependency removed from store:', oldDependency.id);
          }
          break;
      }
    },

    // User presence tracking
    trackUserPresence: (entityType: string, entityId: string) => {
      supabase.auth.getUser().then(({ data: { user } }) => {
        if (!user) return;

        const channelName = `${entityType}-${entityId}-presence`;

        // Don't create duplicate channels
        if (get().presenceChannels[channelName]) {
          return;
        }

        const presenceChannel = supabase.channel(channelName)
          .on('presence', { event: 'sync' }, () => {
            const state = presenceChannel.presenceState();
            const users = Object.values(state).flat();
            set(currentState => ({
              activeUsers: {
                ...currentState.activeUsers,
                [channelName]: users
              }
            }));
          })
          .on('presence', { event: 'join' }, ({ key, newPresences }) => {
            console.log('User joined:', entityType, entityId, newPresences);
          })
          .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
            console.log('User left:', entityType, entityId, leftPresences);
          })
          .subscribe(async (status) => {
            if (status === 'SUBSCRIBED') {
              await presenceChannel.track({
                user_id: user.id,
                email: user.email,
                online_at: new Date().toISOString(),
              });
            }
          });

        set(state => ({
          presenceChannels: {
            ...state.presenceChannels,
            [channelName]: presenceChannel
          }
        }));
      });
    },

    stopTrackingPresence: (entityType: string, entityId: string) => {
      const channelName = `${entityType}-${entityId}-presence`;
      const state = get();

      if (state.presenceChannels[channelName]) {
        state.presenceChannels[channelName].unsubscribe();

        set(currentState => {
          const newPresenceChannels = { ...currentState.presenceChannels };
          const newActiveUsers = { ...currentState.activeUsers };

          delete newPresenceChannels[channelName];
          delete newActiveUsers[channelName];

          return {
            presenceChannels: newPresenceChannels,
            activeUsers: newActiveUsers
          };
        });
      }
    },

    // Polling fallback mechanism
    startPolling: () => {
      const state = get();
      if (state.syncInterval) {
        clearInterval(state.syncInterval);
      }

      console.log('Starting polling fallback for multi-user sync...');

      const interval = setInterval(async () => {
        try {
          if (!get().enablePolling) return;

          console.log('Performing periodic sync for multi-user updates...');
          await get().syncData();
        } catch (error) {
          console.error('Periodic sync failed:', error);
        }
      }, 30000); // Poll every 30 seconds

      set({ syncInterval: interval, enablePolling: true });
    },

    stopPolling: () => {
      const state = get();
      if (state.syncInterval) {
        clearInterval(state.syncInterval);
        set({ syncInterval: null, enablePolling: false });
      }
    },

    // Debug method to test real-time connection
    testRealtimeConnection: () => {
      const channels = supabase.getChannels();
      console.log('🔍 Real-time connection status:');
      console.log(`📊 Total channels: ${channels.length}`);

      channels.forEach((channel, index) => {
        console.log(`📡 Channel ${index + 1}: ${channel.topic} - State: ${channel.state}`);
      });

      if (channels.length === 0) {
        console.log('⚠️ No channels found - real-time not set up');
      } else {
        const activeChannels = channels.filter(ch => ch.state === 'joined');
        console.log(`✅ Active channels: ${activeChannels.length}/${channels.length}`);

        if (activeChannels.length === 0) {
          console.log('❌ No active channels - enabling polling fallback');
          get().startPolling();
        }
      }
    },

    // Manual refresh method for when sync gets out of order
    forceRefresh: async () => {
      console.log('🔄 Force refreshing all data...');
      try {
        await get().syncData();
        console.log('✅ Force refresh completed');
      } catch (error) {
        console.error('❌ Force refresh failed:', error);
      }
    },

    // Refresh specific task data to resolve version conflicts
    refreshTask: async (taskId: string) => {
      try {
        console.log('🔄 Refreshing task data for version sync:', taskId);
        const supabaseTasks = await taskService.getTasks();
        const updatedTask = supabaseTasks.find(t => t.id === taskId);

        if (updatedTask) {
          const transformedTask = transformSupabaseTask(updatedTask);
          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === taskId ? transformedTask : task
            )
          }));
          console.log('✅ Task refreshed successfully');
          return transformedTask;
        }
      } catch (error) {
        console.error('❌ Failed to refresh task:', error);
      }
    },

    // Debug method to check task version in database vs local state
    checkTaskVersion: async (taskId: string) => {
      try {
        const { data: dbTask } = await supabase
          .from('tasks')
          .select('id, title, version, updated_at')
          .eq('id', taskId)
          .single();

        const localTask = get().tasks.find(t => t.id === taskId);

        console.log('🔍 Task Version Check:', {
          taskId,
          title: dbTask?.title || 'Not found',
          database: {
            version: dbTask?.version || 'N/A',
            updated_at: dbTask?.updated_at || 'N/A'
          },
          local: {
            exists: !!localTask,
            title: localTask?.title || 'N/A'
          }
        });
      } catch (error) {
        console.error('❌ Failed to check task version:', error);
      }
    },

    cleanupSubscriptions: () => {
      supabase.removeAllChannels();
    },

    // Complete cleanup for user logout
    cleanup: () => {
      const state = get();

      // Clear all intervals
      if (state.sessionTimeout) {
        clearInterval(state.sessionTimeout);
      }
      if (state.syncInterval) {
        clearInterval(state.syncInterval);
      }

      // Clear all subscriptions
      supabase.removeAllChannels();

      // Reset all state to initial values
      set({
        tasks: [],
        projects: [],
        userGroups: [],
        columns: [],
        folders: [],
        users: [],
        skillsetGroups: [],
        userCapacities: [],
        taskEfforts: [],
        tasksViewMode: 'kanban',
        selectedTreeNode: null,
        expandedTreeNodes: new Set(),
        taskFilters: {
          assignedUsers: [],
          status: [],
          priority: [],
          owners: [],
          assignedGroups: [],
          tags: [],
          dueDateRange: {},
          hasOverdueTasks: false,
          search: undefined,
        },
        loading: {
          tasks: false,
          projects: false,
          userGroups: false,
          columns: false,
          folders: false,
          users: false,
          skillsetGroups: false,
          userCapacities: false,
          taskEfforts: false,
        },
        initialized: false,
        lastRefresh: 0,
        isUpdating: false,
        isSyncing: false,
        syncFailureCount: 0,
        updateCounter: 0,
        sessionTimeout: null,
        syncInterval: null,
        connectionState: 'disconnected',
        reconnectAttempts: 0,
        lastConnectionTime: 0,
        maxReconnectAttempts: 5,
        activeUsers: {},
        presenceChannels: {},
        enablePolling: false
      });
    },

    // Ultra-simplified queue system - just execute directly with minimal protection
    queueUpdate: async (updateFn: () => Promise<void>) => {
      console.log('Executing queued update...');
      try {
        await updateFn();
        console.log('Queued update completed successfully');
      } catch (error) {
        console.error('Update failed:', error);
        throw error;
      }
    },



    // Session health check to detect stale sessions
    checkSessionHealth: async () => {
      try {
        const user = await getCurrentUser();
        if (!user) {
          console.warn('Session health check: No user found');
          return false;
        }

        // Try a simple database operation to check connectivity
        const { data, error } = await supabase
          .from('user_profiles')
          .select('id')
          .eq('id', user.id)
          .limit(1);

        if (error) {
          console.warn('Session health check failed:', error);
          return false;
        }

        return true;
      } catch (error) {
        console.warn('Session health check error:', error);
        return false;
      }
    },

    resetOnStaleSession: async () => {
      console.log('Resetting store due to stale session');

      // Clear all state
      get().cleanup();

      // Try to reinitialize
      try {
        await get().initialize();
      } catch (error) {
        console.error('Failed to reinitialize after stale session:', error);
      }
    },

    // Notification actions
    loadNotifications: async (userId: string) => {
      try {
        set(state => ({
          loading: { ...state.loading, notifications: true }
        }));

        const data = await notificationService.getNotifications(userId);

        // Transform database format to component format
        const transformedNotifications: Notification[] = data.map(item => ({
          id: item.id,
          recipientId: item.recipient_id,
          senderId: item.sender_id,
          type: item.type,
          title: item.title,
          content: item.content,
          taskId: item.task_id || undefined,
          commentId: item.comment_id || undefined,
          isRead: item.is_read,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        }));

        set(state => ({
          notifications: transformedNotifications,
          loading: { ...state.loading, notifications: false }
        }));
      } catch (error) {
        console.error('Failed to load notifications:', error);
        set(state => ({
          loading: { ...state.loading, notifications: false }
        }));
        throw error;
      }
    },

    markNotificationAsRead: async (notificationId: string) => {
      try {
        await notificationService.markAsRead(notificationId);
        set(state => ({
          notifications: state.notifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          )
        }));
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
        throw error;
      }
    },

    markNotificationAsUnread: async (notificationId: string) => {
      try {
        await notificationService.markAsUnread(notificationId);
        set(state => ({
          notifications: state.notifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, isRead: false }
              : notification
          )
        }));
      } catch (error) {
        console.error('Failed to mark notification as unread:', error);
        throw error;
      }
    },

    markAllNotificationsAsRead: async (userId: string) => {
      try {
        await notificationService.markAllAsRead(userId);
        set(state => ({
          notifications: state.notifications.map(notification => ({
            ...notification,
            isRead: true
          }))
        }));
      } catch (error) {
        console.error('Failed to mark all notifications as read:', error);
        throw error;
      }
    },

    getUnreadNotificationCount: (userId: string) => {
      const state = get();
      return state.notifications.filter(n => !n.isRead && n.recipientId === userId).length;
    },

    // Manual refresh function for user-initiated sync
    manualRefresh: async () => {
      console.log('Manual refresh initiated by user...');
      try {
        await get().syncData();
        console.log('Manual refresh completed successfully');
      } catch (error) {
        console.error('Manual refresh failed:', error);
        throw error;
      }
    },

    // Dependency management actions
    loadTaskDependencies: async (taskId: string) => {
      try {
        set(state => ({
          loading: { ...state.loading, taskDependencies: true }
        }));

        const result = await dependencyService.getTaskDependencies(taskId);
        if (result.success) {
          const currentDependencies = get().taskDependencies;
          const updatedDependencies = [
            ...currentDependencies.filter(dep =>
              dep.predecessorTaskId !== taskId && dep.successorTaskId !== taskId
            ),
            ...(result.data || [])
          ];

          set(state => ({
            taskDependencies: updatedDependencies,
            loading: { ...state.loading, taskDependencies: false }
          }));
        }
      } catch (error) {
        console.error('Failed to load task dependencies:', error);
        set(state => ({
          loading: { ...state.loading, taskDependencies: false }
        }));
      }
    },

    createTaskDependency: async (dependency: any) => {
      try {
        const result = await dependencyService.createDependency(dependency);
        if (result.success && result.data) {
          set(state => ({
            taskDependencies: [...state.taskDependencies, result.data!]
          }));

          // Trigger date propagation
          await dependencyPropagationService.handleDependencyChange(
            dependency.predecessorTaskId,
            dependency.successorTaskId,
            'created'
          );
        }
        return result;
      } catch (error) {
        console.error('Failed to create dependency:', error);
        return { success: false, error: 'Failed to create dependency' };
      }
    },

    updateTaskDependency: async (id: string, updates: any) => {
      try {
        const result = await dependencyService.updateDependency(id, updates);
        if (result.success && result.data) {
          set(state => ({
            taskDependencies: state.taskDependencies.map(dep =>
              dep.id === id ? result.data! : dep
            )
          }));
        }
        return result;
      } catch (error) {
        console.error('Failed to update dependency:', error);
        return { success: false, error: 'Failed to update dependency' };
      }
    },

    deleteTaskDependency: async (id: string) => {
      try {
        const dependency = get().taskDependencies.find(dep => dep.id === id);
        const result = await dependencyService.deleteDependency(id);

        if (result.success) {
          set(state => ({
            taskDependencies: state.taskDependencies.filter(dep => dep.id !== id)
          }));

          // Trigger date propagation if dependency existed
          if (dependency) {
            await dependencyPropagationService.handleDependencyChange(
              dependency.predecessorTaskId,
              dependency.successorTaskId,
              'deleted'
            );
          }
        }
        return result;
      } catch (error) {
        console.error('Failed to delete dependency:', error);
        return { success: false, error: 'Failed to delete dependency' };
      }
    },

    // Custom Field actions
    loadCustomFields: async () => {
      try {
        set(state => ({ loading: { ...state.loading, customFields: true } }));
        const supabaseFields = await customFieldService.getCustomFields();
        const transformedFields = supabaseFields.map((field: any) => ({
          id: field.id,
          name: field.name,
          label: field.label,
          fieldType: field.field_type,
          isRequired: field.is_required,
          dropdownOptions: field.dropdown_options,
          defaultValue: field.default_value,
          description: field.description,
          sortOrder: field.sort_order,
          isActive: field.is_active,
          createdAt: field.created_at,
          updatedAt: field.updated_at,
          createdBy: field.created_by,
        }));
        set(state => ({
          customFields: transformedFields,
          loading: { ...state.loading, customFields: false }
        }));
      } catch (error) {
        console.error('Failed to load custom fields:', error);
        set(state => ({ loading: { ...state.loading, customFields: false } }));
        throw error;
      }
    },

    addCustomField: async (field) => {
      try {
        const supabaseField = await customFieldService.createCustomField(field);
        const transformedField = {
          id: supabaseField.id,
          name: supabaseField.name,
          label: supabaseField.label,
          fieldType: supabaseField.field_type,
          isRequired: supabaseField.is_required,
          dropdownOptions: supabaseField.dropdown_options,
          defaultValue: supabaseField.default_value,
          description: supabaseField.description,
          sortOrder: supabaseField.sort_order,
          isActive: supabaseField.is_active,
          createdAt: supabaseField.created_at,
          updatedAt: supabaseField.updated_at,
          createdBy: supabaseField.created_by,
        };
        set(state => ({ customFields: [...state.customFields, transformedField] }));
      } catch (error) {
        console.error('Failed to add custom field:', error);
        throw error;
      }
    },

    updateCustomField: async (id, updates) => {
      try {
        const supabaseField = await customFieldService.updateCustomField(id, updates);
        const transformedField = {
          id: supabaseField.id,
          name: supabaseField.name,
          label: supabaseField.label,
          fieldType: supabaseField.field_type,
          isRequired: supabaseField.is_required,
          dropdownOptions: supabaseField.dropdown_options,
          defaultValue: supabaseField.default_value,
          description: supabaseField.description,
          sortOrder: supabaseField.sort_order,
          isActive: supabaseField.is_active,
          createdAt: supabaseField.created_at,
          updatedAt: supabaseField.updated_at,
          createdBy: supabaseField.created_by,
        };
        set(state => ({
          customFields: state.customFields.map(field =>
            field.id === id ? transformedField : field
          )
        }));
      } catch (error) {
        console.error('Failed to update custom field:', error);
        throw error;
      }
    },

    deleteCustomField: async (id) => {
      try {
        await customFieldService.deleteCustomField(id);
        set(state => ({
          customFields: state.customFields.map(field =>
            field.id === id ? { ...field, isActive: false } : field
          )
        }));
      } catch (error) {
        console.error('Failed to delete custom field:', error);
        throw error;
      }
    },

    reorderCustomFields: async (fieldIds) => {
      try {
        await customFieldService.reorderCustomFields(fieldIds);
        // Update local state to reflect new order
        const reorderedFields = fieldIds.map((id, index) => {
          const field = get().customFields.find(f => f.id === id);
          return field ? { ...field, sortOrder: index } : null;
        }).filter(Boolean);

        set({ customFields: reorderedFields as any[] });
      } catch (error) {
        console.error('Failed to reorder custom fields:', error);
        throw error;
      }
    }
  }))
);

// Expose store to window for debugging (development only)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).useSupabaseStore = useSupabaseStore;
  (window as any).supabase = supabase;
}