# Database Schema Documentation

This document describes the complete database schema for the Project Management Tool.

## 🏷️ Custom Types

### Enums
```sql
-- User roles
CREATE TYPE user_role AS ENUM ('admin', 'user');

-- Task statuses
CREATE TYPE task_status AS ENUM ('todo', 'in_progress', 'review', 'done');

-- Task priorities
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- Dependency types
CREATE TYPE dependency_type AS ENUM ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish');
```

## 📊 Core Tables

### user_profiles
Extends Supabase auth.users with application-specific data.

```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  avatar_url TEXT,
  role user_role DEFAULT 'user',
  group_id UUID REFERENCES user_groups(id),
  skillset_ids UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- Links to Supabase auth system
- Role-based access (admin/user)
- Group membership and skillset assignments
- Automatic profile creation via triggers

### tasks
Main task management with subtask support.

```sql
CREATE TABLE tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  status task_status DEFAULT 'todo',
  priority task_priority DEFAULT 'medium',
  assigned_user_id UUID REFERENCES auth.users(id),
  assigned_users TEXT[] DEFAULT '{}',
  assigned_groups TEXT[] DEFAULT '{}',
  owner_id UUID REFERENCES auth.users(id),
  due_date DATE,
  start_date DATE,
  tags TEXT[] DEFAULT '{}',
  project_id UUID REFERENCES projects(id),
  folder_id UUID REFERENCES folders(id),
  effort JSONB,
  subtasks JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

**Key Features**:
- Full task lifecycle management
- Multiple assignment options (user, groups)
- Hierarchical organization (projects, folders)
- JSONB subtasks for nested task structure
- Effort estimation and tracking

### task_dependencies
Task dependency relationships for project scheduling.

```sql
CREATE TABLE task_dependencies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  predecessor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  successor_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  dependency_type dependency_type DEFAULT 'finish_to_start',
  lag_days INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  UNIQUE(predecessor_task_id, successor_task_id)
);
```

**Key Features**:
- Finish-to-start dependency relationships
- Automatic date calculation support
- Lag time configuration for delays
- Prevents circular dependencies via constraints
- Cascade deletion when tasks are removed

### projects
Project organization and management.

```sql
CREATE TABLE projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  color TEXT NOT NULL,
  start_date DATE,
  end_date DATE,
  folder_id UUID REFERENCES folders(id),
  effort JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### folders
Hierarchical folder structure for organization.

```sql
CREATE TABLE folders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  parent_id UUID REFERENCES folders(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

## 👥 User Management Tables

### user_groups
Team organization and grouping.

```sql
CREATE TABLE user_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### skillset_groups
Skills and competency management.

```sql
CREATE TABLE skillset_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

### user_capacities
Resource planning and availability tracking.

```sql
CREATE TABLE user_capacities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  daily_hours DECIMAL(4,2) NOT NULL,
  weekly_hours DECIMAL(4,2) NOT NULL,
  working_days INTEGER[] NOT NULL,
  effective_from DATE NOT NULL,
  effective_to DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 📝 Activity Tracking Tables

### task_comments
Threaded comments on tasks and subtasks.

```sql
CREATE TABLE task_comments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES task_comments(id),
  edited BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### task_history
Change tracking and audit trail.

```sql
CREATE TABLE task_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  field TEXT NOT NULL,
  old_value TEXT NOT NULL,
  new_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### task_durations
Time tracking for tasks.

```sql
CREATE TABLE task_durations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  status TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🎯 Planning Tables

### task_efforts
Effort estimation and resource planning.

```sql
CREATE TABLE task_efforts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  estimated_hours DECIMAL(6,2) NOT NULL,
  actual_hours DECIMAL(6,2),
  assigned_user_id UUID REFERENCES auth.users(id),
  required_skillsets UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### kanban_columns
Customizable workflow columns.

```sql
CREATE TABLE kanban_columns (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) NOT NULL
);
```

## 🔒 Security Features

### Row Level Security (RLS)
All tables have RLS enabled with policies for:
- **Users**: Can view all data, update own profiles
- **Admins**: Can manage all data and users
- **Creators**: Can manage their own created content

### Key Policies
```sql
-- User profile access
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can update any profile" ON user_profiles FOR UPDATE USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Task management
CREATE POLICY "Users can view all tasks" ON tasks FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create tasks" ON tasks FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

## 🔧 Database Functions & Triggers

### User Creation Trigger
Automatically creates user profiles when users sign up:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    CASE WHEN NEW.email = '<EMAIL>' THEN 'admin'::user_role ELSE 'user'::user_role END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### Update Timestamp Triggers
Automatically update `updated_at` columns on all tables.

## 📈 Indexes

Key indexes for performance:
- `idx_tasks_assigned_user_id` - Task assignment queries
- `idx_tasks_project_id` - Project-based filtering
- `idx_tasks_status` - Status-based queries
- `idx_task_comments_task_id` - Comment retrieval
- `idx_user_capacities_user_id` - Capacity lookups
- `idx_tasks_subtasks` - JSONB subtask queries (GIN index)

## 🔄 Data Relationships

```
auth.users (Supabase)
├── user_profiles (1:1)
├── user_capacities (1:many)
├── tasks (created_by, assigned_user_id)
├── projects (created_by)
└── folders (created_by)

user_groups
├── user_profiles (group_id)
└── tasks (assigned_groups)

projects
├── tasks (project_id)
└── folders (folder_id)

tasks
├── task_comments (task_id)
├── task_history (task_id)
├── task_durations (task_id)
├── task_efforts (task_id)
└── subtasks (JSONB array)
```

## 🗄️ Archive System

### Archive Columns
All main tables (`folders`, `projects`, `tasks`) include archive functionality:

```sql
-- Archive columns added to folders, projects, and tasks
ALTER TABLE folders ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE folders ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE folders ADD COLUMN original_parent_id UUID;

ALTER TABLE projects ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE projects ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE projects ADD COLUMN original_folder_id UUID;

ALTER TABLE tasks ADD COLUMN archived_at TIMESTAMPTZ;
ALTER TABLE tasks ADD COLUMN archived_by UUID REFERENCES auth.users(id);
ALTER TABLE tasks ADD COLUMN original_project_id UUID;
ALTER TABLE tasks ADD COLUMN original_folder_id UUID;
```

### Archive Functions

#### archive_folder_cascade(folder_id, user_id)
Recursively archives a folder and all its contents:
- Archives the folder itself
- Archives all subfolders recursively
- Archives all projects in the folder (using `archive_project_cascade`)
- Archives all tasks directly in the folder

#### archive_project_cascade(project_id, user_id)
Archives a project and all its tasks:
- Archives the project itself
- Archives all tasks in the project
- Preserves original folder location for restoration

#### restore_folder_cascade(folder_id)
Restores a folder and all its contents:
- Restores the folder to its original location
- Restores all subfolders recursively
- Restores all projects in the folder
- Restores all tasks directly in the folder

#### restore_project_cascade(project_id)
Restores a project and all its tasks:
- Restores the project to its original location
- Restores all tasks in the project

#### cleanup_old_archives()
Automatically removes archived items older than 1 week:
- Permanently deletes folders, projects, and tasks archived more than 7 days ago
- Called automatically via scheduled function

### Archive Features
- **Soft Delete**: Items are marked as archived, not permanently deleted
- **Cascade Operations**: Archiving a folder archives all its contents
- **Original Location Tracking**: Stores original parent/folder/project IDs for restoration
- **Admin-Only Access**: Only admin users can view and restore archived items
- **Automatic Cleanup**: Items older than 1 week are permanently deleted
- **Filtered Queries**: Normal operations exclude archived items automatically

This schema supports full project management functionality with proper security, performance, data integrity, and comprehensive archive management.
