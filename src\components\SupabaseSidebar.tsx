import React, { useState, useEffect } from 'react';
import { Layout, Home, CheckSquare, Calendar, Settings, Users, Columns, LayoutGrid, List, Clock, Palette, UserCheck, BarChart3, Loader2, Inbox as InboxIcon, Zap, ChevronDown, ChevronRight, FormInput, Archive } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { useInitializeSupabaseStore } from '../hooks/useSupabaseStore';
import { notificationService } from '../services/supabaseService';
import { getCurrentUser } from '../lib/supabase';
import UserGroupManager from './UserGroupManager';
import Timeline from './Timeline';
import GanttChart from './GanttChart';
import Dashboard from './Dashboard';
import Avatar from './Avatar';
import ProjectManager from './ProjectManager';
import ColumnManager from './ColumnManager';
import SupabaseSettingsPanel from './SupabaseSettings';
import KanbanBoard from './KanbanBoard';
import TaskTreeSidebar from './TaskTreeSidebar';
import TaskListView from './TaskListView';
import SkillsetGroupManager from './SkillsetGroupManager';
import UserManager from './UserManager';
import ResourceCalendar from './ResourceCalendar';
import CustomFieldManager from './CustomFieldManager';
import Analytics from './Analytics';
import Inbox from './Inbox';
import WorkflowManager from './automation/WorkflowManager';
import ArchiveManager from './ArchiveManager';

export default function SupabaseSidebar() {
  const { profile, isAdmin } = useAuth();
  const { tasksViewMode, setTasksViewMode, loading } = useSupabaseStore();
  const { initialized } = useInitializeSupabaseStore();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [unreadCount, setUnreadCount] = useState(0);
  const [isAdminSettingsExpanded, setIsAdminSettingsExpanded] = useState(false);
  const [timelineViewMode, setTimelineViewMode] = useState<'timeline' | 'gantt'>('timeline');

  // Auto-expand admin settings if an admin section is active
  useEffect(() => {
    const adminSectionIds = ['users', 'skillsets', 'groups', 'columns', 'custom-fields', 'archive', 'settings'];
    if (adminSectionIds.includes(activeSection)) {
      setIsAdminSettingsExpanded(true);
    }
  }, [activeSection]);

  // Load unread notification count
  useEffect(() => {
    const loadUnreadCount = async () => {
      try {
        const user = await getCurrentUser();
        if (user) {
          const count = await notificationService.getUnreadCount(user.id);
          setUnreadCount(count);
        }
      } catch (error) {
        console.error('Failed to load unread count:', error);
      }
    };

    if (initialized) {
      loadUnreadCount();
      // Refresh count every 30 seconds
      const interval = setInterval(loadUnreadCount, 30000);
      return () => clearInterval(interval);
    }
  }, [initialized]);

  // Refresh unread count when inbox is opened
  useEffect(() => {
    if (activeSection === 'inbox' && initialized) {
      const refreshUnreadCount = async () => {
        try {
          const user = await getCurrentUser();
          if (user) {
            const count = await notificationService.getUnreadCount(user.id);
            setUnreadCount(count);
          }
        } catch (error) {
          console.error('Failed to refresh unread count:', error);
        }
      };
      refreshUnreadCount();
    }
  }, [activeSection, initialized]);

  // Show loading state while initializing
  if (!initialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your workspace...</p>
        </div>
      </div>
    );
  }

  const mainMenuItems = [
    { icon: Home, label: 'Dashboard', id: 'dashboard' },
    { icon: InboxIcon, label: 'Inbox', id: 'inbox' },
    { icon: CheckSquare, label: 'Tasks', id: 'tasks' },
    { icon: Zap, label: 'Automation', id: 'automation' },
    { icon: Clock, label: 'Resource Management', id: 'calendar' },
    { icon: BarChart3, label: 'Analytics', id: 'analytics' },
  ];

  const adminMenuItems = [
    { icon: UserCheck, label: 'Users', id: 'users' },
    { icon: Palette, label: 'Skillsets', id: 'skillsets' },
    { icon: Users, label: 'User Groups', id: 'groups' },
    { icon: Columns, label: 'Columns', id: 'columns' },
    { icon: FormInput, label: 'Custom Fields', id: 'custom-fields' },
    { icon: Archive, label: 'Archive', id: 'archive' },
    { icon: Settings, label: 'Settings', id: 'settings' },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Dashboard />
          </div>
        );
      case 'inbox':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Inbox />
          </div>
        );
      case 'automation':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <WorkflowManager />
          </div>
        );
      case 'groups':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserGroupManager />
          </div>
        );

      case 'projects':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ProjectManager />
          </div>
        );
      case 'columns':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ColumnManager />
          </div>
        );
      case 'custom-fields':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <CustomFieldManager />
          </div>
        );
      case 'archive':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ArchiveManager />
          </div>
        );
      case 'settings':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SupabaseSettingsPanel />
          </div>
        );
      case 'users':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserManager />
          </div>
        );
      case 'skillsets':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SkillsetGroupManager />
          </div>
        );
      case 'calendar':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <ResourceCalendar />
          </div>
        );
      case 'analytics':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Analytics />
          </div>
        );
      case 'tasks':
        if (tasksViewMode === 'timeline') {
          // Timeline view - full width for timeline
          return (
            <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
              <div className="h-full flex flex-col">
                <div className="flex justify-between items-center p-6 pb-4 flex-shrink-0">
                  <h1 className="text-2xl font-bold text-gray-900">Tasks</h1>
                  <div className="flex items-center gap-4">
                    {loading.tasks && (
                      <div className="flex items-center gap-2 text-gray-600">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span className="text-sm">Syncing...</span>
                      </div>
                    )}
                    <div className="flex bg-gray-200 rounded-lg p-1">
                      <button
                        onClick={() => setTasksViewMode('kanban')}
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                          tasksViewMode === 'kanban'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <LayoutGrid className="w-4 h-4" />
                        Kanban
                      </button>
                      <button
                        onClick={() => setTasksViewMode('list')}
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                          tasksViewMode === 'list'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <List className="w-4 h-4" />
                        List
                      </button>
                      <button
                        onClick={() => setTasksViewMode('timeline')}
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                          tasksViewMode === 'timeline'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <Calendar className="w-4 h-4" />
                        Timeline
                      </button>
                    </div>

                    {/* Timeline/Gantt Toggle */}
                    <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setTimelineViewMode('timeline')}
                        className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                          timelineViewMode === 'timeline'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        Timeline
                      </button>
                      <button
                        onClick={() => setTimelineViewMode('gantt')}
                        className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                          timelineViewMode === 'gantt'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        Gantt
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex-1 overflow-hidden">
                  {timelineViewMode === 'gantt' ? (
                    <GanttChart
                      tasks={useSupabaseStore.getState().tasks}
                      onTaskUpdate={(task) => {
                        // Handle task updates if needed
                        console.log('Task updated from Gantt:', task);
                      }}
                    />
                  ) : (
                    <Timeline />
                  )}
                </div>
              </div>
            </div>
          );
        } else if (tasksViewMode === 'kanban') {
          // Kanban view - no tree sidebar, full width for kanban board
          return (
            <div className="grid grid-cols-[256px_1fr] h-screen">
              <div className="bg-gray-900">
                {/* Sidebar content is rendered separately */}
              </div>
              <div className="bg-gray-50 overflow-hidden">
                <div className="h-full flex flex-col">
                  <div className="flex justify-between items-center p-6 pb-4 flex-shrink-0">
                    <h1 className="text-2xl font-bold text-gray-900">Tasks</h1>
                    <div className="flex items-center gap-4">
                      {loading.tasks && (
                        <div className="flex items-center gap-2 text-gray-600">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="text-sm">Syncing...</span>
                        </div>
                      )}
                      <div className="flex bg-gray-200 rounded-lg p-1">
                        <button
                          onClick={() => setTasksViewMode('kanban')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            tasksViewMode === 'kanban'
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          <LayoutGrid className="w-4 h-4" />
                          Kanban
                        </button>
                        <button
                          onClick={() => setTasksViewMode('list')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            tasksViewMode === 'list'
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          <List className="w-4 h-4" />
                          List
                        </button>
                        <button
                          onClick={() => setTasksViewMode('timeline')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                            tasksViewMode === 'timeline'
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          <Calendar className="w-4 h-4" />
                          Timeline
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <KanbanBoard />
                  </div>
                </div>
              </div>
            </div>
          );
        } else {
          // List view - with tree sidebar
          return (
            <div className="fixed inset-0 left-64 bg-gray-50 overflow-hidden">
              <div className="h-full flex">
                <div className="w-64 bg-gray-800 overflow-y-auto">
                  <TaskTreeSidebar />
                </div>
                <div className="flex-1 bg-gray-50 overflow-y-auto">
                  <div className="h-full flex flex-col">
                    <div className="flex justify-between items-center p-6 pb-4 flex-shrink-0">
                      <h1 className="text-2xl font-bold text-gray-900">Tasks</h1>
                      <div className="flex items-center gap-4">
                        {loading.tasks && (
                          <div className="flex items-center gap-2 text-gray-600">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            <span className="text-sm">Syncing...</span>
                          </div>
                        )}
                        <div className="flex bg-gray-200 rounded-lg p-1">
                          <button
                            onClick={() => setTasksViewMode('kanban')}
                            className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                              tasksViewMode === 'kanban'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            <LayoutGrid className="w-4 h-4" />
                            Kanban
                          </button>
                          <button
                            onClick={() => setTasksViewMode('list')}
                            className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                              tasksViewMode === 'list'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            <List className="w-4 h-4" />
                            List
                          </button>
                          <button
                            onClick={() => setTasksViewMode('timeline')}
                            className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                              tasksViewMode === 'timeline'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            <Calendar className="w-4 h-4" />
                            Timeline
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="flex-1">
                      <TaskListView />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        }
      default:
        return null;
    }
  };

  return (
    <>
      <div className="w-64 bg-gray-900 h-screen fixed left-0 top-0 text-white p-4 overflow-y-auto z-10">
        <div className="flex items-center gap-2 mb-8">
          <Layout className="w-8 h-8 text-blue-400" />
          <h1 className="text-xl font-bold">TaskFlow</h1>
        </div>
        
        {/* User Info */}
        <div className="mb-6 p-3 bg-gray-800 rounded-lg">
          <div className="flex items-center gap-3">
            <Avatar
              src={profile?.avatar_url}
              name={profile?.name || 'User'}
              size="md"
              className="bg-blue-600"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {profile?.name || 'User'}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {profile?.email}
              </p>
            </div>
          </div>
          {isAdmin && (
            <div className="mt-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                Administrator
              </span>
            </div>
          )}
        </div>
        
        <nav className="space-y-2">
          {/* Main menu items */}
          {mainMenuItems.map(({ icon: Icon, label, id }) => (
            <button
              key={id}
              onClick={() => setActiveSection(id)}
              className={`flex items-center gap-3 w-full p-3 rounded-lg transition-colors ${
                activeSection === id ? 'bg-gray-800' : 'hover:bg-gray-800'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="flex-1 text-left">{label}</span>
              {id === 'inbox' && unreadCount > 0 && (
                <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </button>
          ))}

          {/* Admin Settings collapsible section */}
          <div className="mt-4">
            <button
              onClick={() => setIsAdminSettingsExpanded(!isAdminSettingsExpanded)}
              className="flex items-center gap-3 w-full p-3 rounded-lg transition-colors hover:bg-gray-800"
            >
              <Settings className="w-5 h-5" />
              <span className="flex-1 text-left">Admin Settings</span>
              {isAdminSettingsExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>

            {/* Admin submenu items */}
            {isAdminSettingsExpanded && (
              <div className="ml-4 mt-2 space-y-1">
                {adminMenuItems.map(({ icon: Icon, label, id }) => (
                  <button
                    key={id}
                    onClick={() => setActiveSection(id)}
                    className={`flex items-center gap-3 w-full p-2 rounded-lg transition-colors text-sm ${
                      activeSection === id ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="flex-1 text-left">{label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </nav>
      </div>

      {renderContent()}
    </>
  );
}
